import React, { useState, ReactNode } from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';

interface ControlContainerProps {
  title: string;
  icon: ReactNode;
  children: ReactNode;
  position?: 'topleft' | 'topright' | 'bottomleft' | 'bottomright';
  initialCollapsed?: boolean;
  className?: string;
}

const ControlContainer: React.FC<ControlContainerProps> = ({
  title,
  icon,
  children,
  position = 'topright',
  initialCollapsed = true,
  className = '',
}) => {
  const [collapsed, setCollapsed] = useState(initialCollapsed);

  const positionClasses = {
    topleft: 'top-2 left-2',
    topright: 'top-2 right-2',
    bottomleft: 'bottom-2 left-2',
    bottomright: 'bottom-2 right-2',
  };

  return (
    <div
      className={`absolute z-[1000] ${positionClasses[position]} ${className}`}
    >
      <div className="military-control-container rounded-md shadow-lg overflow-hidden max-w-[250px]">
        <div
          className="header flex items-center justify-between px-2 py-1 cursor-pointer hover:bg-gray-700"
          onClick={() => setCollapsed(!collapsed)}
        >
          <div className="flex items-center space-x-1">
            {icon}
            <span className="font-bold text-xs tracking-wider">{title}</span>
          </div>
          {collapsed ? (
            <ChevronDown size={12} />
          ) : (
            <ChevronUp size={12} />
          )}
        </div>

        {!collapsed && (
          <div className="content p-2 max-h-[calc(100vh-200px)] overflow-y-auto">
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default ControlContainer;
