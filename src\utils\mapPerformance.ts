import maplibregl from 'maplibre-gl';
import { Incident, Response } from '@/types/incident';

// Performance optimization utilities for MapLibre

export interface PerformanceConfig {
  maxMarkersBeforeClustering: number;
  clusterRadius: number;
  maxZoomForClustering: number;
  debounceDelay: number;
  useWebGL: boolean;
  enableLOD: boolean; // Level of Detail
  maxRenderDistance: number; // meters
}

export const defaultPerformanceConfig: PerformanceConfig = {
  maxMarkersBeforeClustering: 100,
  clusterRadius: 50,
  maxZoomForClustering: 14,
  debounceDelay: 100,
  useWebGL: true,
  enableLOD: true,
  maxRenderDistance: 50000 // 50km
};

// Debounce utility for map events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

// Calculate distance between two points (Haversine formula)
export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// Filter incidents/responses based on current map view and performance settings
export function filterByViewport(
  items: (Incident | Response)[],
  map: maplibregl.Map,
  config: PerformanceConfig
): (Incident | Response)[] {
  if (!map || !config.enableLOD) return items;

  const bounds = map.getBounds();
  const center = map.getCenter();
  const zoom = map.getZoom();

  return items.filter(item => {
    // Check if item is within bounds
    const itemLng = item.location.longitude;
    const itemLat = item.location.latitude;

    if (!bounds.contains([itemLng, itemLat])) {
      return false;
    }

    // Check distance from center for LOD
    const distance = calculateDistance(
      center.lat,
      center.lng,
      itemLat,
      itemLng
    );

    if (distance > config.maxRenderDistance) {
      return false;
    }

    // Reduce detail at lower zoom levels
    if (zoom < 10) {
      // Only show high priority items at low zoom
      return item.severity === 'HIGH' || item.severity === 'CRITICAL';
    }

    return true;
  });
}

// Create optimized marker pool for reuse
export class MarkerPool {
  private pool: maplibregl.Marker[] = [];
  private activeMarkers: Map<string, maplibregl.Marker> = new Map();

  getMarker(id: string): maplibregl.Marker | null {
    return this.activeMarkers.get(id) || null;
  }

  createMarker(id: string, element: HTMLElement, lngLat: [number, number]): maplibregl.Marker {
    let marker = this.pool.pop();

    if (!marker) {
      marker = new maplibregl.Marker();
    }

    marker.setLngLat(lngLat);
    marker.getElement().replaceWith(element);

    this.activeMarkers.set(id, marker);
    return marker;
  }

  removeMarker(id: string): void {
    const marker = this.activeMarkers.get(id);
    if (marker) {
      marker.remove();
      this.activeMarkers.delete(id);
      this.pool.push(marker);
    }
  }

  clear(): void {
    this.activeMarkers.forEach(marker => marker.remove());
    this.activeMarkers.clear();
  }

  getActiveCount(): number {
    return this.activeMarkers.size;
  }
}

// Optimize symbol rendering with canvas-based approach
export function createOptimizedSymbol(
  type: string,
  color: string,
  size: number,
  text?: string
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) throw new Error('Could not get canvas context');

  const dpr = window.devicePixelRatio || 1;
  canvas.width = size * dpr;
  canvas.height = size * dpr;
  canvas.style.width = `${size}px`;
  canvas.style.height = `${size}px`;

  ctx.scale(dpr, dpr);

  const center = size / 2;
  const radius = center - 2;

  // Draw symbol shape
  ctx.fillStyle = color;
  ctx.strokeStyle = '#000';
  ctx.lineWidth = 1;

  ctx.beginPath();
  ctx.arc(center, center, radius, 0, 2 * Math.PI);
  ctx.fill();
  ctx.stroke();

  // Add text if provided
  if (text) {
    ctx.fillStyle = '#fff';
    ctx.font = `bold ${Math.max(8, size / 4)}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, center, center);
  }

  return canvas;
}

// Batch update utility for multiple markers
export function batchUpdateMarkers(
  updateOperations: Array<{
    id: string;
    action: 'add' | 'update' | 'remove';
    data?: any;
  }>,
  markerPool: MarkerPool,
  map: maplibregl.Map
): void {
  // Group updates by type for efficiency
  const adds: any[] = [];
  const updates: any[] = [];
  const removes: string[] = [];

  updateOperations.forEach(operation => {
    switch (operation.action) {
      case 'add':
        adds.push(operation);
        break;
      case 'update':
        updates.push(operation);
        break;
      case 'remove':
        removes.push(operation.id);
        break;
    }
  });

  // Process removes first
  removes.forEach(id => markerPool.removeMarker(id));

  // Process adds and updates
  [...adds, ...updates].forEach(operation => {
    if (operation.data) {
      // Create or update marker based on data
      // Implementation would depend on specific marker structure
    }
  });
}

// Memory management utilities
export function cleanupUnusedResources(map: maplibregl.Map): void {
  // Remove unused sources
  const style = map.getStyle();
  if (style && style.sources) {
    Object.keys(style.sources).forEach(sourceId => {
      if (sourceId.startsWith('temp-') || sourceId.startsWith('old-')) {
        try {
          map.removeSource(sourceId);
        } catch (error) {
          console.warn(`Could not remove source ${sourceId}:`, error);
        }
      }
    });
  }

  // Force garbage collection if available
  if (window.gc) {
    window.gc();
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  private startTimes: Map<string, number> = new Map();

  startTimer(name: string): void {
    this.startTimes.set(name, performance.now());
  }

  endTimer(name: string): number {
    const startTime = this.startTimes.get(name);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.startTimes.delete(name);

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const times = this.metrics.get(name)!;
    times.push(duration);

    // Keep only last 100 measurements
    if (times.length > 100) {
      times.shift();
    }

    return duration;
  }

  getAverageTime(name: string): number {
    const times = this.metrics.get(name);
    if (!times || times.length === 0) return 0;

    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getMetrics(): Record<string, { average: number; count: number }> {
    const result: Record<string, { average: number; count: number }> = {};

    this.metrics.forEach((times, name) => {
      result[name] = {
        average: this.getAverageTime(name),
        count: times.length
      };
    });

    return result;
  }

  clear(): void {
    this.metrics.clear();
    this.startTimes.clear();
  }
}

// Adaptive performance configuration
export function getAdaptiveConfig(
  deviceInfo: {
    memory?: number;
    cores?: number;
    gpu?: string;
  },
  currentPerformance: {
    fps?: number;
    renderTime?: number;
  }
): Partial<PerformanceConfig> {
  const config: Partial<PerformanceConfig> = {};

  // Adjust based on device capabilities
  if (deviceInfo.memory && deviceInfo.memory < 4) {
    // Low memory device
    config.maxMarkersBeforeClustering = 50;
    config.enableLOD = true;
    config.maxRenderDistance = 25000;
  }

  // Adjust based on current performance
  if (currentPerformance.fps && currentPerformance.fps < 30) {
    // Poor performance, reduce quality
    config.maxMarkersBeforeClustering = Math.max(25, (config.maxMarkersBeforeClustering || 100) / 2);
    config.debounceDelay = 200;
  }

  return config;
}
