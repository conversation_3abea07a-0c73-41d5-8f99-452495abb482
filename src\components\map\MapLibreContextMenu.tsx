import React, { useEffect } from 'react';
import { MapPin, Shield, Plus } from 'lucide-react';

interface MapLibreContextMenuProps {
  position: { x: number; y: number };
  onAddIncident: () => void;
  onAddResponse: () => void;
  onClose: () => void;
}

const MapLibreContextMenu: React.FC<MapLibreContextMenuProps> = ({
  position,
  onAddIncident,
  onAddResponse,
  onClose
}) => {
  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.maplibre-context-menu')) {
        onClose();
      }
    };

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleEscape);
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  return (
    <div
      className="maplibre-context-menu absolute z-[2000] bg-military-panel border border-military-border rounded-md shadow-lg overflow-hidden"
      style={{
        left: position.x,
        top: position.y,
        minWidth: '180px'
      }}
    >
      <ul className="py-1">
        <li 
          className="px-4 py-2 hover:bg-military-navy cursor-pointer flex items-center text-military-white transition-colors"
          onClick={onAddIncident}
        >
          <MapPin className="w-4 h-4 mr-2 text-military-accent" />
          <span className="font-military-body text-sm">Add Incident</span>
        </li>
        <li 
          className="px-4 py-2 hover:bg-military-navy cursor-pointer flex items-center text-military-white transition-colors"
          onClick={onAddResponse}
        >
          <Shield className="w-4 h-4 mr-2 text-military-accent" />
          <span className="font-military-body text-sm">Add Response</span>
        </li>
        <li className="border-t border-military-border my-1"></li>
        <li 
          className="px-4 py-2 hover:bg-military-navy cursor-pointer flex items-center text-military-white transition-colors"
          onClick={onClose}
        >
          <Plus className="w-4 h-4 mr-2 text-military-accent transform rotate-45" />
          <span className="font-military-body text-sm">Close</span>
        </li>
      </ul>
    </div>
  );
};

export default MapLibreContextMenu;
