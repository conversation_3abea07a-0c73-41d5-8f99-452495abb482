/* Military-themed filter components */

/* Filter panel */
.filter-panel {
  background-color: #2A3A2A;
  border: 1px solid #5E8E3E;
  color: #F5F5F5;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  clip-path: polygon(
    0 0,
    calc(100% - 8px) 0,
    100% 8px,
    100% 100%,
    8px 100%,
    0 calc(100% - 8px)
  );
  margin-bottom: 16px;
}

/* Filter chips/badges */
.filter-chip {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  margin: 2px;
  font-size: 11px;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #F5F5F5;
  background-color: #4A5D23;
  border: 1px solid #5E8E3E;
  clip-path: polygon(
    0 0,
    calc(100% - 4px) 0,
    100% 4px,
    100% 100%,
    4px 100%,
    0 calc(100% - 4px)
  );
  transition: all 0.2s ease;
}

.filter-chip:hover {
  background-color: #5E8E3E;
  border-color: #7FAE5E;
}

.filter-chip-icon {
  margin-right: 4px;
  font-size: 10px;
}

.filter-chip-close {
  margin-left: 4px;
  cursor: pointer;
  opacity: 0.7;
}

.filter-chip-close:hover {
  opacity: 1;
}

/* Filter types */
.filter-chip-type {
  background-color: #1C2541;
  border-color: #3A506B;
}

.filter-chip-severity {
  background-color: #C1121F;
  border-color: #E63946;
}

.filter-chip-severity.low {
  background-color: #2ECC40;
  border-color: #4CD964;
}

.filter-chip-severity.medium {
  background-color: #FFDC00;
  border-color: #FFE74C;
  color: #1A1A1A;
}

.filter-chip-severity.high {
  background-color: #FF851B;
  border-color: #FFA94D;
}

.filter-chip-severity.critical {
  background-color: #C1121F;
  border-color: #E63946;
}

.filter-chip-status {
  background-color: #4A5D23;
  border-color: #5E8E3E;
}

.filter-chip-date {
  background-color: #1C2541;
  border-color: #3A506B;
}

.filter-chip-search {
  background-color: #7FAE5E;
  border-color: #9BC53D;
  color: #1A1A1A;
}

.filter-chip-spatial {
  background-color: #FFBF00;
  border-color: #FFD700;
  color: #1A1A1A;
}

.filter-chip-chart {
  background-color: #4A5D23;
  border-color: #5E8E3E;
}

/* Reset filters button */
.reset-filters-btn {
  background-color: #2A3A2A;
  color: #F5F5F5;
  border: 1px solid #5E8E3E;
  padding: 4px 8px;
  font-size: 12px;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  clip-path: polygon(
    0 0,
    calc(100% - 4px) 0,
    100% 4px,
    100% 100%,
    4px 100%,
    0 calc(100% - 4px)
  );
  transition: all 0.2s ease;
}

.reset-filters-btn:hover {
  background-color: #4A5D23;
  border-color: #7FAE5E;
}

/* Spatial filter panel */
.spatial-filter-panel {
  background-color: rgba(42, 58, 42, 0.9);
  border: 1px solid #5E8E3E;
  color: #F5F5F5;
  font-family: 'Roboto Mono', 'Courier New', monospace;
  padding: 8px;
  clip-path: polygon(
    0 0,
    calc(100% - 8px) 0,
    100% 8px,
    100% 100%,
    8px 100%,
    0 calc(100% - 8px)
  );
  z-index: 1000;
}

/* Interactive charts */
.interactive-chart {
  position: relative;
}

.interactive-chart .recharts-wrapper {
  cursor: pointer;
}

.interactive-chart .recharts-tooltip-wrapper {
  background-color: rgba(42, 58, 42, 0.9) !important;
  border: 1px solid #5E8E3E !important;
  border-radius: 0 !important;
  clip-path: polygon(
    0 0,
    calc(100% - 4px) 0,
    100% 4px,
    100% 100%,
    4px 100%,
    0 calc(100% - 4px)
  ) !important;
}

.interactive-chart .recharts-default-tooltip {
  background-color: rgba(42, 58, 42, 0.9) !important;
  border: none !important;
  border-radius: 0 !important;
}

.interactive-chart .recharts-tooltip-item {
  color: #F5F5F5 !important;
}

.interactive-chart .recharts-tooltip-label {
  color: #FFBF00 !important;
}

/* Chart filter indicators */
.chart-filtered-element {
  stroke: #FFBF00 !important;
  stroke-width: 2px !important;
}

/* Military-themed button for small filter chips */
.military-button-sm {
  background-color: #2A3A2A !important;
  color: #F5F5F5 !important;
  border: 1px solid #5E8E3E !important;
  transition: all 0.2s ease;
  padding: 2px 4px !important;
  font-size: 10px !important;
  min-width: 20px !important;
  min-height: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  clip-path: polygon(
    0 0,
    calc(100% - 3px) 0,
    100% 3px,
    100% 100%,
    3px 100%,
    0 calc(100% - 3px)
  );
}

.military-button-sm:hover {
  background-color: #4A5D23 !important;
  color: #FFFFFF !important;
  border-color: #7FAE5E !important;
}
