import React, { useState, useEffect, useCallback, useRef } from 'react';
import maplibregl from 'maplibre-gl';
import Button from '@/components/ui/Button';
import {
  MapPin,
  Home,
  Trash2,
  Maximize2,
  Minimize2,
  Crosshair,
  Map,
  Grid,
  Mountain,
  Globe,
  Moon,
  Satellite,
  Layers,
  Target,
  Circle,
  Square,
  Pencil,
  Box,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  Eye,
  Settings
} from 'lucide-react';

interface MapLibreToolbarProps {
  map: maplibregl.Map | null;
  activeBaseLayer: string;
  onBaseLayerChange: (layer: string) => void;
  mapMode: 'normal' | 'cluster' | 'heatmap';
  onMapModeChange: (mode: 'normal' | 'cluster' | 'heatmap') => void;
  is3D: boolean;
  onToggle3D: () => void;
}

const MapLibreToolbar: React.FC<MapLibreToolbarProps> = ({
  map,
  activeBaseLayer,
  onBaseLayerChange,
  mapMode,
  onMapModeChange,
  is3D,
  onToggle3D
}) => {
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [windowSize, setWindowSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  // Auto-hide and collapsible states
  const [isAutoHideEnabled, setIsAutoHideEnabled] = useState(true);
  const [isToolbarVisible, setIsToolbarVisible] = useState(true);
  const [collapsedSections, setCollapsedSections] = useState({
    navigation: false,
    drawing: false,
    terrain: false,
    baseLayers: false,
    viewModes: false,
    legend: false,
    controls: false
  });
  const [lastActivity, setLastActivity] = useState(Date.now());
  const autoHideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const AUTO_HIDE_DELAY = 3000; // 3 seconds

  // Auto-hide functionality
  const updateActivity = useCallback(() => {
    setLastActivity(Date.now());
    if (!isToolbarVisible) {
      setIsToolbarVisible(true);
    }
  }, [isToolbarVisible]);

  // Auto-hide timer effect
  useEffect(() => {
    if (!isAutoHideEnabled) return;

    const checkAutoHide = () => {
      const now = Date.now();
      if (now - lastActivity > AUTO_HIDE_DELAY && isToolbarVisible) {
        setIsToolbarVisible(false);
      }
    };

    autoHideTimeoutRef.current = setInterval(checkAutoHide, 1000);

    return () => {
      if (autoHideTimeoutRef.current) {
        clearInterval(autoHideTimeoutRef.current);
      }
    };
  }, [isAutoHideEnabled, lastActivity, isToolbarVisible]);

  // Track window size for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Track mouse movement for auto-hide
  useEffect(() => {
    if (!isAutoHideEnabled) return;

    const handleMouseMove = () => updateActivity();
    const handleKeyPress = () => updateActivity();

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('keydown', handleKeyPress);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [updateActivity, isAutoHideEnabled]);

  // Track mouse coordinates
  useEffect(() => {
    if (!map) return;

    const handleMouseMove = (e: maplibregl.MapMouseEvent) => {
      setCoordinates({
        lat: e.lngLat.lat,
        lng: e.lngLat.lng
      });
      updateActivity(); // Update activity on map interaction
    };

    map.on('mousemove', handleMouseMove);

    return () => {
      map.off('mousemove', handleMouseMove);
    };
  }, [map, updateActivity]);

  // Toggle section collapse
  const toggleSection = useCallback((section: keyof typeof collapsedSections) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
    updateActivity();
  }, [updateActivity]);

  // Toggle auto-hide
  const toggleAutoHide = useCallback(() => {
    setIsAutoHideEnabled(prev => !prev);
    if (!isAutoHideEnabled) {
      setIsToolbarVisible(true);
    }
    updateActivity();
  }, [isAutoHideEnabled, updateActivity]);

  const handleBaseLayerChange = (layer: string) => {
    onBaseLayerChange(layer);
    updateActivity();
  };

  const handleFullscreen = () => {
    if (!map) return;

    const container = map.getContainer();
    if (!document.fullscreenElement) {
      container.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
    updateActivity();
  };

  const handleHome = () => {
    if (!map) return;

    map.flyTo({
      center: [71.5249, 34.0151], // Pakistan coordinates
      zoom: is3D ? 8 : 6, // Better zoom for 3D terrain
      pitch: is3D ? 60 : 0,
      bearing: 0,
      duration: 2000
    });
    updateActivity();
  };

  const handleCurrentLocation = () => {
    if (!map) return;

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          map.flyTo({
            center: [longitude, latitude],
            zoom: 15,
            duration: 2000
          });
          updateActivity();
        },
        (error) => {
          console.warn('Could not get current location:', error);
          // Fallback to home location
          handleHome();
        }
      );
    } else {
      console.warn('Geolocation is not supported by this browser');
      handleHome();
    }
  };

  const adjustTerrainExaggeration = (exaggeration: number) => {
    if (!map || !is3D) return;

    try {
      map.setTerrain({
        source: 'terrarium',
        exaggeration: exaggeration
      });
    } catch (error) {
      console.warn('Could not adjust terrain exaggeration:', error);
    }
    updateActivity();
  };

  const clearDrawnItems = () => {
    if (!map) return;

    // Remove all drawn layers
    const layers = map.getStyle().layers;
    if (layers) {
      layers.forEach(layer => {
        if (layer.id.startsWith('drawn-')) {
          map.removeLayer(layer.id);
        }
      });
    }

    // Remove all drawn sources
    const sources = map.getStyle().sources;
    if (sources) {
      Object.keys(sources).forEach(sourceId => {
        if (sourceId.startsWith('drawn-')) {
          map.removeSource(sourceId);
        }
      });
    }
    updateActivity();
  };

  // Enhanced drawing tool handlers with activity tracking
  const handleDrawingTool = (mode: string) => {
    const event = new CustomEvent('maplibre:startDraw', { detail: { mode } });
    window.dispatchEvent(event);
    updateActivity();
  };

  // Determine layout based on window size
  const isSmallScreen = windowSize.width < 768;
  const isShortScreen = windowSize.height < 600;
  const shouldUseCompactLayout = isSmallScreen || isShortScreen;

  // Collapsible section component
  const CollapsibleSection: React.FC<{
    title: string;
    sectionKey: keyof typeof collapsedSections;
    children: React.ReactNode;
    className?: string;
  }> = ({ title, sectionKey, children, className = '' }) => {
    const isCollapsed = collapsedSections[sectionKey];

    return (
      <div className={`military-control-container rounded-md shadow-lg overflow-hidden ${className}`}>
        <div
          className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider border-b border-military-border cursor-pointer hover:bg-military-navy flex items-center justify-between"
          onClick={() => toggleSection(sectionKey)}
        >
          <span>{title}</span>
          {isCollapsed ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
        </div>
        {!isCollapsed && (
          <div className="transition-all duration-200 ease-in-out">
            {children}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Auto-hide toggle button - always visible */}
      <div className="absolute top-2 left-2 z-[1001]">
        <Button
          size="sm"
          variant="ghost"
          className={`military-button ${isAutoHideEnabled ? 'active' : ''}`}
          title={isAutoHideEnabled ? 'Disable Auto-hide' : 'Enable Auto-hide'}
          onClick={toggleAutoHide}
        >
          <Eye size={14} />
        </Button>
      </div>

      {/* Main toolbar container with auto-hide */}
      <div className={`transition-all duration-300 ease-in-out ${
        isToolbarVisible ? 'opacity-100' : 'opacity-30'
      }`}>
        {/* Coordinates display - bottom center */}
        {coordinates && !isSmallScreen && isToolbarVisible && (
          <div className="absolute toolbar-bottom-center z-[1000] military-control-container rounded-md shadow-lg p-2">
            <div className="text-xs font-mono text-military-white">
              <div>LAT: {coordinates.lat.toFixed(6)}</div>
              <div>LNG: {coordinates.lng.toFixed(6)}</div>
            </div>
          </div>
        )}

        {/* TOP-LEFT: Navigation controls and drawing tools */}
        {isToolbarVisible && (
          <div className="absolute toolbar-top-left z-[1000] flex flex-col gap-2">
            {/* Navigation controls */}
            <CollapsibleSection title="NAVIGATION" sectionKey="navigation">
              <div className="flex flex-col gap-1 p-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Home View"
                  onClick={handleHome}
                >
                  <Home size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Current Location"
                  onClick={handleCurrentLocation}
                >
                  <Crosshair size={14} />
                </Button>
              </div>
            </CollapsibleSection>

            {/* Drawing tools section */}
            <CollapsibleSection title="DRAWING TOOLS" sectionKey="drawing">
              <div className="flex flex-col gap-1 p-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Draw Point"
                  onClick={() => handleDrawingTool('draw_point')}
                >
                  <MapPin size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Draw Line"
                  onClick={() => handleDrawingTool('draw_line_string')}
                >
                  <Pencil size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Draw Polygon"
                  onClick={() => handleDrawingTool('draw_polygon')}
                >
                  <Square size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Draw Circle"
                  onClick={() => handleDrawingTool('draw_circle')}
                >
                  <Circle size={14} />
                </Button>
              </div>
            </CollapsibleSection>

            {/* Terrain controls (3D mode only) */}
            {is3D && (
              <CollapsibleSection title="TERRAIN" sectionKey="terrain">
                <div className="flex flex-col gap-1 p-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="military-button text-button"
                    title="Low Terrain (1x)"
                    onClick={() => adjustTerrainExaggeration(1)}
                  >
                    1x
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="military-button text-button"
                    title="Normal Terrain (1.5x)"
                    onClick={() => adjustTerrainExaggeration(1.5)}
                  >
                    1.5x
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="military-button text-button"
                    title="High Terrain (2x)"
                    onClick={() => adjustTerrainExaggeration(2)}
                  >
                    2x
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="military-button text-button"
                    title="Extreme Terrain (3x)"
                    onClick={() => adjustTerrainExaggeration(3)}
                  >
                    3x
                  </Button>
                </div>
              </CollapsibleSection>
            )}
          </div>
        )}

        {/* TOP-RIGHT: Base layer controls and view modes */}
        {isToolbarVisible && (
          <div className="absolute toolbar-top-right z-[1000] flex flex-col gap-2">
            {/* Base layer controls */}
            <CollapsibleSection title="BASE LAYERS" sectionKey="baseLayers">
              <div className="flex flex-col gap-1 p-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${activeBaseLayer === 'satellite' ? 'active' : ''}`}
                  title="Satellite"
                  onClick={() => handleBaseLayerChange('satellite')}
                >
                  <Satellite size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${activeBaseLayer === 'terrain' ? 'active' : ''}`}
                  title="Terrain"
                  onClick={() => handleBaseLayerChange('terrain')}
                >
                  <Mountain size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${activeBaseLayer === 'osm' ? 'active' : ''}`}
                  title="OpenStreetMap"
                  onClick={() => handleBaseLayerChange('osm')}
                >
                  <Globe size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${activeBaseLayer === 'dark' ? 'active' : ''}`}
                  title="Dark"
                  onClick={() => handleBaseLayerChange('dark')}
                >
                  <Moon size={14} />
                </Button>
              </div>
            </CollapsibleSection>

            {/* View mode controls */}
            <CollapsibleSection title="VIEW MODES" sectionKey="viewModes">
              <div className="flex flex-col gap-1 p-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${mapMode === 'normal' ? 'active' : ''}`}
                  title="Normal View"
                  onClick={() => {
                    onMapModeChange('normal');
                    updateActivity();
                  }}
                >
                  <MapPin size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${mapMode === 'cluster' ? 'active' : ''}`}
                  title="Cluster View"
                  onClick={() => {
                    onMapModeChange('cluster');
                    updateActivity();
                  }}
                >
                  <Target size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${mapMode === 'heatmap' ? 'active' : ''}`}
                  title="Heatmap View"
                  onClick={() => {
                    onMapModeChange('heatmap');
                    updateActivity();
                  }}
                >
                  <Grid size={14} />
                </Button>
              </div>
            </CollapsibleSection>
          </div>
        )}

        {/* BOTTOM-LEFT: Legend and tactical symbols */}
        {isToolbarVisible && (
          <div className="absolute toolbar-bottom-left z-[1000] flex flex-col gap-2">
            <CollapsibleSection title="LEGEND" sectionKey="legend">
              <div className="flex flex-col gap-1 p-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Toggle Legend"
                  onClick={() => {
                    // Toggle legend visibility
                    const event = new CustomEvent('maplibre:toggleLegend');
                    window.dispatchEvent(event);
                    updateActivity();
                  }}
                >
                  <Layers size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Tactical Symbols"
                  onClick={() => {
                    // Toggle tactical symbols
                    const event = new CustomEvent('maplibre:toggleSymbols');
                    window.dispatchEvent(event);
                    updateActivity();
                  }}
                >
                  <Target size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Symbol Settings"
                  onClick={() => {
                    // Open symbol settings
                    const event = new CustomEvent('maplibre:symbolSettings');
                    window.dispatchEvent(event);
                    updateActivity();
                  }}
                >
                  <Settings size={14} />
                </Button>
              </div>
            </CollapsibleSection>
          </div>
        )}

        {/* BOTTOM-RIGHT: Map controls */}
        {isToolbarVisible && (
          <div className="absolute toolbar-bottom-right z-[1000]">
            <CollapsibleSection title="MAP CONTROLS" sectionKey="controls">
              <div className="flex flex-col gap-1 p-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Clear All Drawings"
                  onClick={clearDrawnItems}
                >
                  <Trash2 size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className={`military-button ${is3D ? 'active' : ''}`}
                  title={is3D ? 'Switch to 2D' : 'Switch to 3D'}
                  onClick={() => {
                    onToggle3D();
                    updateActivity();
                  }}
                >
                  <Box size={14} />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button"
                  title="Fullscreen"
                  onClick={handleFullscreen}
                >
                  {isFullscreen ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
                </Button>
              </div>
            </CollapsibleSection>
          </div>
        )}
      </div>
    </>
  );
};

export default MapLibreToolbar;
