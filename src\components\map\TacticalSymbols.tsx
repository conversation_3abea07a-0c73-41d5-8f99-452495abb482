import React from 'react';
import L from 'leaflet';
import { IncidentType, ActionType } from '@/types/incident';

// NATO APP-6 inspired tactical symbols for different incident types
export const tacticalSymbols = {
  // Original incident types
  [IncidentType.PHYSICAL_RAID]: {
    symbol: 'physical_raid',
    color: '#FF4136',
    description: 'Physical Raid'
  },
  [IncidentType.FIRE_RAID]: {
    symbol: 'fire_raid',
    color: '#FF851B',
    description: 'Fire Raid'
  },
  [IncidentType.AMBUSH]: {
    symbol: 'ambush',
    color: '#B10DC9',
    description: 'Ambush'
  },
  [IncidentType.SNIPING]: {
    symbol: 'sniping',
    color: '#85144b',
    description: 'Sniping'
  },
  [IncidentType.POST_OVERRUN]: {
    symbol: 'post_overrun',
    color: '#FF4136',
    description: 'Post Overrun'
  },
  [IncidentType.POST_FIRE]: {
    symbol: 'post_fire',
    color: '#FF851B',
    description: 'Post Fire'
  },
  [IncidentType.DEMONSTRATION]: {
    symbol: 'demonstration',
    color: '#FFDC00',
    description: 'Demonstration'
  },
  [IncidentType.TARGET_KILLING]: {
    symbol: 'target_killing',
    color: '#FF4136',
    description: 'Target Killing'
  },
  [IncidentType.ARSON]: {
    symbol: 'arson',
    color: '#FF851B',
    description: 'Arson'
  },

  // TS (Tactical Situation) incident types
  [IncidentType.TS_ACTIVITY]: {
    symbol: 'ts_activity',
    color: '#FF4136',
    description: 'TS Activity'
  },
  [IncidentType.TS_INFIL]: {
    symbol: 'ts_infil',
    color: '#0074D9',
    description: 'TS Infiltration'
  },
  [IncidentType.TS_PRESENCE]: {
    symbol: 'ts_presence',
    color: '#FF851B',
    description: 'TS Presence'
  },
  [IncidentType.TS_MOV]: {
    symbol: 'ts_mov',
    color: '#FFDC00',
    description: 'TS Movement'
  },
  [IncidentType.TS_TASKEEL]: {
    symbol: 'ts_taskeel',
    color: '#7FDBFF',
    description: 'TS Taskeel'
  },
  [IncidentType.TS_SB]: {
    symbol: 'ts_sb',
    color: '#B10DC9',
    description: 'TS SB'
  },
  [IncidentType.TS_EXTORTION]: {
    symbol: 'ts_extortion',
    color: '#85144b',
    description: 'TS Extortion'
  },
  [IncidentType.TS_SUSPECT]: {
    symbol: 'ts_suspect',
    color: '#39CCCC',
    description: 'TS Suspect'
  },
  [IncidentType.TS_MEETING]: {
    symbol: 'ts_meeting',
    color: '#01FF70',
    description: 'TS Meeting'
  },
  [IncidentType.TS_SEEN]: {
    symbol: 'ts_seen',
    color: '#F012BE',
    description: 'TS Seen'
  },
  [IncidentType.TS_TGT_KILLING]: {
    symbol: 'ts_tgt_killing',
    color: '#FF4136',
    description: 'TS Target Killing'
  },
  [IncidentType.TS_JIRGA]: {
    symbol: 'ts_jirga',
    color: '#FFDC00',
    description: 'TS Jirga'
  },

  [IncidentType.OTHER]: {
    symbol: 'other',
    color: '#AAAAAA',
    description: 'Other'
  }
};

// NATO APP-6 inspired tactical symbols for different response types (all blue)
export const responseSymbols = {
  [ActionType.ADO]: {
    symbol: 'ado',
    color: '#0074D9',
    description: 'Area Dominating Ops'
  },
  [ActionType.ASO]: {
    symbol: 'aso',
    color: '#0074D9',
    description: 'Area Sanitization Ops'
  },
  [ActionType.IBO]: {
    symbol: 'ibo',
    color: '#0074D9',
    description: 'Int Based Ops'
  },
  [ActionType.SEARCH_OPS]: {
    symbol: 'search_ops',
    color: '#0074D9',
    description: 'Search Ops'
  },
  [ActionType.SEARCH_AND_CLEARANCE]: {
    symbol: 'search_clearance',
    color: '#0074D9',
    description: 'Search and Clearance Ops'
  },
  [ActionType.COMPOUND_SEARCH]: {
    symbol: 'compound_search',
    color: '#0074D9',
    description: 'Compound Search Ops'
  },
  [ActionType.CARDON_AND_SEARCH]: {
    symbol: 'cardon_search',
    color: '#0074D9',
    description: 'Cardon and Search Ops'
  },
  [ActionType.ROUTE_CLEARANCE]: {
    symbol: 'route_clearance',
    color: '#0074D9',
    description: 'Route Clearance'
  },
  [ActionType.ROUTE_PICQUETTING]: {
    symbol: 'route_picquetting',
    color: '#0074D9',
    description: 'Route Picquetting'
  },
  [ActionType.ROUTE_PATROLLING]: {
    symbol: 'route_patrolling',
    color: '#0074D9',
    description: 'Route Patrolling'
  },
  [ActionType.ROUTE_RECCE]: {
    symbol: 'route_recce',
    color: '#0074D9',
    description: 'Route Recce'
  },
  [ActionType.IO_CAMPAIGN]: {
    symbol: 'io_campaign',
    color: '#0074D9',
    description: 'Information Ops Campaign'
  },
  [ActionType.CIMIC]: {
    symbol: 'cimic',
    color: '#0074D9',
    description: 'Civil Military Coord Ops'
  },
  [ActionType.QIPS]: {
    symbol: 'qips',
    color: '#0074D9',
    description: 'Quick Impact Projects'
  },
  [ActionType.AIR_OPS]: {
    symbol: 'air_ops',
    color: '#0074D9',
    description: 'Air Ops'
  },
  [ActionType.DRONE_STRIKES]: {
    symbol: 'drone_strikes',
    color: '#0074D9',
    description: 'Drone Strikes'
  },
  [ActionType.ISR_MISSIONS]: {
    symbol: 'isr_missions',
    color: '#0074D9',
    description: 'ISR Missions'
  },
  [ActionType.RESCUE_OPS]: {
    symbol: 'rescue_ops',
    color: '#0074D9',
    description: 'Rescue Ops'
  },
  [ActionType.HOSTAGE_RESCUE]: {
    symbol: 'hostage_rescue',
    color: '#0074D9',
    description: 'Hostage Rescue Ops'
  },
  [ActionType.ROUTE_BD]: {
    symbol: 'route_bd',
    color: '#0074D9',
    description: 'Route BD'
  },
  [ActionType.LVL1_SE_CLEARANCE]: {
    symbol: 'lvl1_se_clearance',
    color: '#0074D9',
    description: 'Lvl 1 S&E Clearance'
  },
  [ActionType.LVL2_SE_CLEARANCE]: {
    symbol: 'lvl2_se_clearance',
    color: '#0074D9',
    description: 'Lvl 2 S&E Clearance'
  },
  [ActionType.LVL3_SE_CLEARANCE]: {
    symbol: 'lvl3_se_clearance',
    color: '#0074D9',
    description: 'Lvl 3 S&E Clearance'
  },
  [ActionType.TECH_SWEEP]: {
    symbol: 'tech_sweep',
    color: '#0074D9',
    description: 'Tech Sweep Ops'
  },
  [ActionType.NONE]: {
    symbol: 'none',
    color: '#0074D9',
    description: 'None'
  }
};

// Get custom symbology from localStorage if available
const getCustomSymbology = () => {
  try {
    const savedSymbology = localStorage.getItem('symbology');
    if (savedSymbology) {
      const parsed = JSON.parse(savedSymbology);
      return {
        incidents: parsed.incidents || {},
        responses: parsed.responses || {}
      };
    }
  } catch (error) {
    console.error('Failed to parse saved symbology:', error);
  }
  return { incidents: {}, responses: {} };
};

// Create a tactical symbol icon for a specific incident type
export const createTacticalIcon = (type: IncidentType, size: number = 32) => {
  // Try to get custom symbol first
  const customSymbology = getCustomSymbology();
  const customSymbol = customSymbology.incidents[type];

  // Use custom symbol if available, otherwise fall back to default
  const symbol = customSymbol || tacticalSymbols[type] || tacticalSymbols[IncidentType.OTHER];

  // Create custom SVG path if shape and/or text are specified
  let svgPath = '';

  if (customSymbol && (customSymbol.shape || customSymbol.text)) {
    // Generate SVG path based on shape
    const shape = customSymbol.shape || 'circle';
    switch (shape) {
      case 'diamond':
        svgPath = '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" />';
        break;
      case 'square':
        svgPath = '<rect x="4" y="4" width="24" height="24" fill-opacity="0.8" />';
        break;
      case 'triangle':
        svgPath = '<polygon points="16,2 30,30 2,30" fill-opacity="0.8" />';
        break;
      case 'hexagon':
        svgPath = '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" />';
        break;
      case 'star':
        svgPath = '<polygon points="16,2 19,12 30,12 21,18 25,28 16,22 7,28 11,18 2,12 13,12" fill-opacity="0.8" />';
        break;
      case 'circle':
      default:
        svgPath = '<circle cx="16" cy="16" r="14" fill-opacity="0.8" />';
        break;
    }

    // Add text if specified
    if (customSymbol.text) {
      svgPath += `<text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">${customSymbol.text}</text>`;
    }
  } else {
    // Use default SVG path
    svgPath = getSvgPathForSymbol(symbol.symbol);
  }

  // Create SVG icon
  const svgIcon = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
      <g fill="${symbol.color}" stroke="#000" stroke-width="1">
        ${svgPath}
      </g>
    </svg>
  `;

  // Convert SVG to data URL
  const svgBase64 = btoa(svgIcon);
  const dataUrl = `data:image/svg+xml;base64,${svgBase64}`;

  // Create Leaflet icon
  return L.icon({
    iconUrl: dataUrl,
    iconSize: [size, size],
    iconAnchor: [size/2, size/2],
    popupAnchor: [0, -size/2],
    className: `tactical-symbol tactical-symbol-${type.toLowerCase()}`
  });
};

// Get SVG path for a specific tactical symbol
const getSvgPathForSymbol = (symbolType: string): string => {
  switch (symbolType) {
    // Original incident symbols - Enhanced NATO-style tactical symbols
    case 'physical_raid':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,12 L24,12 M8,20 L24,20" stroke="#fff" stroke-width="2.5" /><circle cx="16" cy="16" r="3" fill="#fff" />';

    case 'fire_raid':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,6 Q12,10 14,14 Q10,18 16,22 Q22,18 18,14 Q20,10 16,6" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

    case 'ambush':
      return '<polygon points="16,2 2,16 16,30 30,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,16 L24,16 M16,8 L16,24" stroke="#fff" stroke-width="2.5" /><circle cx="12" cy="12" r="2" fill="#fff" /><circle cx="20" cy="20" r="2" fill="#fff" />';

    case 'sniping':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16 M16,6 L16,26" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="6" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="2" fill="#fff" />';

    case 'post_overrun':
      return '<rect x="3" y="3" width="26" height="26" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M7,7 L25,25 M7,25 L25,7" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="3" fill="#fff" />';

    case 'post_fire':
      return '<rect x="3" y="3" width="26" height="26" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,7 Q12,11 14,15 Q10,19 16,23 Q22,19 18,15 Q20,11 16,7" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

    case 'demonstration':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="11" r="2.5" fill="#fff" /><path d="M8,20 Q16,25 24,20" stroke="#fff" stroke-width="2" fill="none" />';

    case 'target_killing':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,8 L24,24 M8,24 L24,8" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="5" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="1.5" fill="#fff" />';

    case 'arson':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,6 Q12,10 14,14 Q10,18 16,22 Q22,18 18,14 Q20,10 16,6" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

    // TS (Tactical Situation) symbols - Enhanced with better visibility
    case 'ts_activity':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="3" fill="#fff" /><path d="M16,8 L16,24 M8,16 L24,16" stroke="#fff" stroke-width="1.5" />';

    case 'ts_infil':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16" stroke="#fff" stroke-width="3" /><path d="M20,10 L26,16 L20,22" stroke="#fff" stroke-width="2.5" fill="none" /><circle cx="8" cy="16" r="2" fill="#fff" />';

    case 'ts_presence':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.4" /><circle cx="16" cy="16" r="3" fill="#fff" />';

    case 'ts_mov':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16" stroke="#fff" stroke-width="3" /><path d="M18,10 L26,16 L18,22" stroke="#fff" stroke-width="2.5" fill="none" /><path d="M10,10 L18,16 L10,22" stroke="#fff" stroke-width="2" fill="none" />';

    case 'ts_taskeel':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="21" r="2.5" fill="#fff" /><path d="M10,16 L22,16" stroke="#fff" stroke-width="1.5" />';

    case 'ts_sb':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><rect x="8" y="10" width="16" height="12" fill="#fff" fill-opacity="0.3" stroke="#fff" stroke-width="1" /><text x="16" y="18" font-size="8" text-anchor="middle" fill="#fff" font-weight="bold">SB</text>';

    case 'ts_extortion':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="none" /><text x="16" y="18" font-size="7" text-anchor="middle" fill="#fff" font-weight="bold">EXT</text>';

    case 'ts_suspect':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="12" r="4" fill="#fff" /><path d="M12,20 Q16,24 20,20" stroke="#fff" stroke-width="2" fill="none" /><text x="16" y="26" font-size="6" text-anchor="middle" fill="#fff" font-weight="bold">SUSP</text>';

    case 'ts_meeting':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="11" r="2.5" fill="#fff" /><ellipse cx="16" cy="20" rx="8" ry="3" fill="#fff" fill-opacity="0.5" stroke="#fff" stroke-width="1" />';

    case 'ts_seen':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 Q16,6 26,16 Q16,26 6,16 Z" fill="#fff" fill-opacity="0.5" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="4" fill="#000" /><circle cx="16" cy="16" r="2" fill="#fff" />';

    case 'ts_tgt_killing':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,8 L24,24 M8,24 L24,8" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="6" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="2" fill="#fff" />';

    case 'ts_jirga':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="10" r="2" fill="#fff" /><circle cx="16" cy="10" r="2" fill="#fff" /><circle cx="22" cy="10" r="2" fill="#fff" /><ellipse cx="16" cy="20" rx="10" ry="4" fill="#fff" fill-opacity="0.3" stroke="#fff" stroke-width="1.5" /><path d="M8,22 Q16,18 24,22" stroke="#fff" stroke-width="1.5" fill="none" />';

    // Response symbols (all blue)
    case 'ado':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">ADO</text>';

    case 'aso':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">ASO</text>';

    case 'ibo':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">IBO</text>';

    case 'search_ops':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" />';

    case 'search_clearance':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" /><path d="M16,22 L16,26" stroke="#fff" stroke-width="1.5" />';

    case 'compound_search':
      return '<rect x="4" y="4" width="24" height="24" fill-opacity="0.8" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1.5" fill="none" />';

    case 'cardon_search':
      return '<rect x="4" y="4" width="24" height="24" fill-opacity="0.8" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="1.5" fill="none" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1" fill="none" />';

    case 'route_clearance':
      return '<path d="M4,16 L28,16" stroke="#fff" stroke-width="3" /><path d="M8,10 L8,22 M24,10 L24,22" stroke="#fff" stroke-width="1.5" />';

    case 'route_picquetting':
      return '<path d="M4,16 L28,16" stroke="#fff" stroke-width="3" /><circle cx="8" cy="10" r="2" fill="#fff" /><circle cx="16" cy="10" r="2" fill="#fff" /><circle cx="24" cy="10" r="2" fill="#fff" /><circle cx="8" cy="22" r="2" fill="#fff" /><circle cx="16" cy="22" r="2" fill="#fff" /><circle cx="24" cy="22" r="2" fill="#fff" />';

    case 'route_patrolling':
      return '<path d="M4,16 L28,16" stroke="#fff" stroke-width="3" /><path d="M8,10 L24,10 M8,22 L24,22" stroke="#fff" stroke-width="1.5" />';

    case 'route_recce':
      return '<path d="M4,16 L28,16" stroke="#fff" stroke-width="3" /><path d="M10,16 Q16,10 22,16 Q16,22 10,16 Z" stroke="#fff" stroke-width="1" fill="none" />';

    case 'io_campaign':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">IO</text>';

    case 'cimic':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><path d="M10,12 L22,12 M10,20 L22,20 M16,8 L16,24" stroke="#fff" stroke-width="1.5" />';

    case 'qips':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" /><text x="16" y="20" font-size="8" text-anchor="middle" fill="#fff">QIPS</text>';

    case 'air_ops':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><path d="M8,16 L24,16 M16,8 L16,24 M10,10 L22,22 M10,22 L22,10" stroke="#fff" stroke-width="1" />';

    case 'drone_strikes':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" /><path d="M8,16 L24,16 M16,8 L16,24" stroke="#fff" stroke-width="1" /><circle cx="16" cy="16" r="4" stroke="#fff" stroke-width="1" fill="none" />';

    case 'isr_missions':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><path d="M8,16 Q16,8 24,16 Q16,24 8,16 Z" fill="#fff" fill-opacity="0.4" />';

    case 'rescue_ops':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" /><path d="M10,16 L22,16 M16,10 L16,22" stroke="#fff" stroke-width="2" />';

    case 'hostage_rescue':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" /><circle cx="12" cy="14" r="2" fill="#fff" /><circle cx="20" cy="14" r="2" fill="#fff" /><path d="M10,16 L22,16 M16,16 L16,22" stroke="#fff" stroke-width="1.5" />';

    case 'route_bd':
      return '<path d="M4,16 L28,16" stroke="#fff" stroke-width="3" /><path d="M16,8 L16,24" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="4" stroke="#fff" stroke-width="1" fill="none" />';

    case 'lvl1_se_clearance':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">L1</text>';

    case 'lvl2_se_clearance':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">L2</text>';

    case 'lvl3_se_clearance':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" /><text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">L3</text>';

    case 'tech_sweep':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><path d="M8,16 L24,16 M16,8 L16,24" stroke="#fff" stroke-width="1.5" /><path d="M10,10 L22,22 M10,22 L22,10" stroke="#fff" stroke-width="1" />';

    case 'other':
    default:
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.8" /><text x="16" y="20" font-size="14" text-anchor="middle" fill="#fff">?</text>';
  }
};

// Create a range ring with specified radius in kilometers
export const createRangeRing = (center: L.LatLng, radiusKm: number, options?: L.CircleOptions) => {
  const defaultOptions: L.CircleOptions = {
    color: '#5E8E3E',
    weight: 2,
    opacity: 0.7,
    fill: false,
    dashArray: '5, 5',
    ...options
  };

  // Convert km to meters for Leaflet
  const radiusMeters = radiusKm * 1000;

  return L.circle(center, {
    radius: radiusMeters,
    ...defaultOptions
  });
};

// Create a line of sight visualization
export const createLineOfSight = (
  origin: L.LatLng,
  targetPoint: L.LatLng,
  options?: L.PolylineOptions
) => {
  const defaultOptions: L.PolylineOptions = {
    color: '#FFD700',
    weight: 2,
    opacity: 0.8,
    dashArray: '10, 5',
    ...options
  };

  return L.polyline([origin, targetPoint], defaultOptions);
};

// Create a response symbol icon for a specific action type
export const createResponseIcon = (type: ActionType, size: number = 32) => {
  // Try to get custom symbol first
  const customSymbology = getCustomSymbology();
  const customSymbol = customSymbology.responses[type];

  // Use custom symbol if available, otherwise fall back to default
  const symbol = customSymbol || responseSymbols[type] || responseSymbols[ActionType.NONE];

  // Create custom SVG path if shape and/or text are specified
  let svgPath = '';

  if (customSymbol && (customSymbol.shape || customSymbol.text)) {
    // Generate SVG path based on shape
    const shape = customSymbol.shape || 'circle';
    switch (shape) {
      case 'diamond':
        svgPath = '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.8" />';
        break;
      case 'square':
        svgPath = '<rect x="4" y="4" width="24" height="24" fill-opacity="0.8" />';
        break;
      case 'triangle':
        svgPath = '<polygon points="16,2 30,30 2,30" fill-opacity="0.8" />';
        break;
      case 'hexagon':
        svgPath = '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.8" />';
        break;
      case 'star':
        svgPath = '<polygon points="16,2 19,12 30,12 21,18 25,28 16,22 7,28 11,18 2,12 13,12" fill-opacity="0.8" />';
        break;
      case 'circle':
      default:
        svgPath = '<circle cx="16" cy="16" r="14" fill-opacity="0.8" />';
        break;
    }

    // Add text if specified
    if (customSymbol.text) {
      svgPath += `<text x="16" y="20" font-size="10" text-anchor="middle" fill="#fff">${customSymbol.text}</text>`;
    }
  } else {
    // Use default SVG path
    svgPath = getSvgPathForSymbol(symbol.symbol);
  }

  // Create SVG icon
  const svgIcon = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
      <g fill="${symbol.color}" stroke="#000" stroke-width="1">
        ${svgPath}
      </g>
    </svg>
  `;

  // Convert SVG to data URL
  const svgBase64 = btoa(svgIcon);
  const dataUrl = `data:image/svg+xml;base64,${svgBase64}`;

  // Create Leaflet icon
  return L.icon({
    iconUrl: dataUrl,
    iconSize: [size, size],
    iconAnchor: [size/2, size/2],
    popupAnchor: [0, -size/2],
    className: `response-symbol response-symbol-${type.toLowerCase()}`
  });
};

export default {
  tacticalSymbols,
  responseSymbols,
  createTacticalIcon,
  createResponseIcon,
  createRangeRing,
  createLineOfSight
};
