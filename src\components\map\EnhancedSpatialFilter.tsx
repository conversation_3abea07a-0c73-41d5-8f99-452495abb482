import React, { useEffect, useState, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import * as turf from '@turf/turf';
import { useIncidentStore } from '@/store/incidentStore';
import { useFilterStore } from '@/store/filterStore';
import { Incident } from '@/types/incident';
import Button from '@/components/ui/Button';
import { Map, X, Filter } from 'lucide-react';

interface EnhancedSpatialFilterProps {
  incidents: Incident[];
  drawnItems: L.FeatureGroup | null;
}

const EnhancedSpatialFilter: React.FC<EnhancedSpatialFilterProps> = ({ incidents, drawnItems }) => {
  const map = useMap();
  const [showPanel, setShowPanel] = useState(false);
  const [filteredIncidents, setFilteredIncidents] = useState<Incident[]>([]);
  const { setSpatialFilter } = useFilterStore();
  const { setFilter } = useIncidentStore();

  // Reference to store the GeoJSON of drawn shapes
  const drawnShapesRef = useRef<GeoJSON.FeatureCollection>({
    type: 'FeatureCollection',
    features: []
  });

  // Listen for draw events and update spatial filter
  useEffect(() => {
    if (!map || !drawnItems) return;

    // Function to update the spatial filter based on drawn items
    const updateSpatialFilter = () => {
      try {
        // Convert drawn items to GeoJSON
        const drawnShapes = drawnItems.toGeoJSON() as GeoJSON.FeatureCollection;
        drawnShapesRef.current = drawnShapes;

        // If there are no drawn shapes, reset the filter
        if (drawnShapes.features.length === 0) {
          setSpatialFilter({ active: false });
          setShowPanel(false);
          return;
        }

        // Filter incidents that are within any of the drawn shapes
        const filtered = incidents.filter(incident => {
          // Create a GeoJSON point for the incident
          const point = turf.point([incident.location.longitude, incident.location.latitude]);

          // Check if the point is within any of the drawn shapes
          return drawnShapes.features.some(feature => {
            try {
              if (feature.geometry.type === 'Point') {
                // For point features, check if they're close enough (within 100m)
                return turf.distance(point, feature as any) < 0.1; // 0.1 km = 100m
              } else if (feature.geometry.type === 'LineString') {
                // For line features, check if point is within buffer distance
                const line = feature as GeoJSON.Feature<GeoJSON.LineString>;
                const buffered = turf.buffer(line, 0.1); // 0.1 km buffer
                return turf.booleanPointInPolygon(point, buffered);
              } else if (feature.geometry.type === 'Polygon' || feature.geometry.type === 'MultiPolygon') {
                // For polygon features, check if point is inside
                return turf.booleanPointInPolygon(point, feature as any);
              } else {
                return false;
              }
            } catch (error) {
              console.error('Error in spatial filtering:', error);
              return false;
            }
          });
        });

        // Update filtered incidents
        setFilteredIncidents(filtered);

        // Show the filter panel
        setShowPanel(true);

        // Update the spatial filter in the store
        setSpatialFilter({
          active: true,
          polygon: drawnShapes.features.find(f =>
            f.geometry.type === 'Polygon' || f.geometry.type === 'MultiPolygon'
          ) as GeoJSON.Polygon | undefined
        });

        // Apply the spatial filter to the incident store
        // We'll use the IDs of the filtered incidents
        const filteredIds = filtered.map(incident => incident.id);

        // Create a custom filter function for the incident store
        const spatialFilterFn = (incident: Incident) => filteredIds.includes(incident.id);

        // Apply the filter
        setFilter({ customFilter: spatialFilterFn });
      } catch (error) {
        console.error('Error updating spatial filter:', error);
      }
    };

    // Listen for draw events
    map.on('draw:created', updateSpatialFilter);
    map.on('draw:edited', updateSpatialFilter);
    map.on('draw:deleted', updateSpatialFilter);

    // Add a custom control for spatial filtering
    const SpatialFilterControl = L.Control.extend({
      options: {
        position: 'topright'
      },
      onAdd: function() {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
        const button = L.DomUtil.create('a', 'leaflet-control-spatial-filter', container);
        button.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>';
        button.href = '#';
        button.title = 'Draw to filter by area';

        L.DomEvent.on(button, 'click', function(e) {
          L.DomEvent.stopPropagation(e);
          L.DomEvent.preventDefault(e);

          // Toggle the draw control
          // @ts-ignore
          const drawControl = map.drawControl;
          if (drawControl) {
            // Find the polygon draw button and click it
            const polygonButton = document.querySelector('.leaflet-draw-draw-polygon');
            if (polygonButton) {
              (polygonButton as HTMLElement).click();
            }
          }
        });

        return container;
      }
    });

    const filterControl = new SpatialFilterControl();
    map.addControl(filterControl);

    return () => {
      map.off('draw:created', updateSpatialFilter);
      map.off('draw:edited', updateSpatialFilter);
      map.off('draw:deleted', updateSpatialFilter);
      map.removeControl(filterControl);
    };
  }, [map, incidents, drawnItems, setSpatialFilter, setFilter]);

  // Function to reset the spatial filter
  const resetFilter = () => {
    if (drawnItems) {
      drawnItems.clearLayers();
      drawnShapesRef.current.features = [];
      setFilteredIncidents([]);
      setShowPanel(false);
      setSpatialFilter({ active: false });

      // Reset the spatial filter in the incident store
      setFilter({ customFilter: undefined });
    }
  };

  return (
    <>
      {showPanel && (
        <div className="spatial-filter-panel absolute bottom-4 left-4 bg-military-panel border border-military-border p-3 rounded-md shadow-lg z-[1000] max-w-xs">
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center">
              <Map size={16} className="mr-2 text-military-accent" />
              <h3 className="text-sm font-medium text-military-white">SPATIAL FILTER</h3>
            </div>
            <Button
              size="xs"
              variant="ghost"
              onClick={resetFilter}
              className="military-button"
              title="Clear filter"
            >
              <X size={14} />
            </Button>
          </div>

          <div className="text-xs mb-2 text-military-white">
            <span className="text-military-accent font-bold">{filteredIncidents.length}</span> incidents within selected area
          </div>

          <div className="flex space-x-2">
            <Button
              size="xs"
              variant="military"
              className="w-full"
              onClick={() => {
                // Zoom to the bounds of the filtered incidents
                if (filteredIncidents.length > 0) {
                  // Store current view before zooming
                  const currentCenter = map.getCenter();
                  const currentZoom = map.getZoom();

                  // Calculate bounds
                  const bounds = L.latLngBounds(
                    filteredIncidents.map(inc =>
                      L.latLng(inc.location.latitude, inc.location.longitude)
                    )
                  );

                  // Add a button to return to previous view
                  const returnButton = document.createElement('button');
                  returnButton.innerHTML = 'Return to Previous View';
                  returnButton.className = 'military-button return-to-view-btn';
                  returnButton.style.position = 'absolute';
                  returnButton.style.top = '10px';
                  returnButton.style.left = '50%';
                  returnButton.style.transform = 'translateX(-50%)';
                  returnButton.style.zIndex = '1000';
                  returnButton.style.padding = '5px 10px';
                  returnButton.style.backgroundColor = '#2A3A2A';
                  returnButton.style.color = '#F5F5F5';
                  returnButton.style.border = '1px solid #5E8E3E';
                  returnButton.style.borderRadius = '4px';
                  returnButton.style.cursor = 'pointer';

                  returnButton.onclick = () => {
                    map.setView(currentCenter, currentZoom);
                    document.body.removeChild(returnButton);
                  };

                  // Add the button to the body
                  document.body.appendChild(returnButton);

                  // Auto-remove after 10 seconds
                  setTimeout(() => {
                    if (document.body.contains(returnButton)) {
                      document.body.removeChild(returnButton);
                    }
                  }, 10000);

                  // Fit bounds with animation
                  map.fitBounds(bounds, {
                    padding: [50, 50],
                    animate: true,
                    duration: 0.5
                  });
                }
              }}
            >
              Zoom to Results
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default EnhancedSpatialFilter;
