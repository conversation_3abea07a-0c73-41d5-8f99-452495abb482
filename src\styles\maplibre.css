/* MapLibre GL JS Custom Styles */

/* Map container */
.maplibregl-map {
  font-family: inherit;
}

/* Control styles */
.maplibregl-ctrl-group {
  background: rgba(26, 30, 35, 0.9) !important;
  border: 1px solid #586173 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.maplibregl-ctrl-group button {
  background-color: transparent !important;
  color: #F5F5F5 !important;
  border: none !important;
  width: 30px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s !important;
}

.maplibregl-ctrl-group button:hover {
  background-color: rgba(88, 97, 115, 0.3) !important;
}

.maplibregl-ctrl-group button:not(:last-child) {
  border-bottom: 1px solid #586173 !important;
}

/* Popup styles */
.maplibregl-popup {
  max-width: 300px !important;
}

.maplibregl-popup-content {
  background: #1a1e23 !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
  border-radius: 8px !important;
  padding: 0 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.maplibregl-popup-close-button {
  color: #F5F5F5 !important;
  font-size: 18px !important;
  padding: 4px !important;
  background: transparent !important;
  border: none !important;
  cursor: pointer !important;
}

.maplibregl-popup-close-button:hover {
  background: rgba(88, 97, 115, 0.3) !important;
  border-radius: 2px !important;
}

.maplibregl-popup-tip {
  border-top-color: #1a1e23 !important;
  border-bottom-color: #1a1e23 !important;
}

/* Attribution */
.maplibregl-ctrl-attrib {
  background: rgba(26, 30, 35, 0.8) !important;
  color: #F5F5F5 !important;
  font-size: 10px !important;
}

.maplibregl-ctrl-attrib a {
  color: #0074D9 !important;
}

/* Scale control */
.maplibregl-ctrl-scale {
  background: rgba(26, 30, 35, 0.8) !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
  border-radius: 2px !important;
  padding: 2px 4px !important;
  font-size: 10px !important;
}

/* Military control container */
.military-control-container {
  background: rgba(26, 30, 35, 0.95);
  border: 1px solid #586173;
  backdrop-filter: blur(4px);
  min-width: fit-content;
}

.military-control-container .header {
  background: rgba(43, 48, 56, 0.8);
  border-bottom: 1px solid #586173;
  color: #F5F5F5;
}

.military-control-container .content {
  background: rgba(26, 30, 35, 0.95);
  color: #F5F5F5;
}

/* Control panel headers */
.military-control-container .text-xs {
  white-space: nowrap;
  min-width: 60px;
}

/* Button container alignment */
.military-control-container .flex {
  align-items: center;
  justify-content: center;
}

/* Military button styles */
.military-button {
  background: rgba(43, 48, 56, 0.8) !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
  transition: all 0.2s ease !important;
  font-family: 'Orbitron', monospace !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  min-height: 32px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  font-size: 10px !important;
}

.military-button:hover {
  background: rgba(88, 97, 115, 0.8) !important;
  border-color: #0074D9 !important;
  color: #F5F5F5 !important;
}

.military-button.active {
  background: rgba(0, 116, 217, 0.8) !important;
  border-color: #0074D9 !important;
  color: #F5F5F5 !important;
}

/* Ensure icons are properly sized within buttons */
.military-button svg {
  width: 14px !important;
  height: 14px !important;
  flex-shrink: 0 !important;
}

/* Special styling for text-only buttons (terrain controls) */
.military-button.text-button {
  font-size: 9px !important;
  font-weight: 700 !important;
}

/* Marker styles */
.incident-marker {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.incident-marker:hover {
  transform: scale(1.2);
}

.response-marker {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.response-marker:hover {
  transform: scale(1.2);
}

/* Tactical symbol styles */
.tactical-symbol {
  position: relative;
  z-index: 10;
}

.tactical-symbol:hover {
  z-index: 20;
}

.response-symbol {
  position: relative;
  z-index: 10;
}

.response-symbol:hover {
  z-index: 20;
}

/* Ensure tactical symbols render properly */
.tactical-symbol svg,
.response-symbol svg {
  display: block;
  width: 100%;
  height: 100%;
}

.cluster-marker {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.cluster-marker:hover {
  transform: scale(1.1);
}

/* Drawing styles */
.mapbox-gl-draw_ctrl-draw-btn {
  background: rgba(43, 48, 56, 0.8) !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
}

.mapbox-gl-draw_ctrl-draw-btn:hover {
  background: rgba(88, 97, 115, 0.8) !important;
}

.mapbox-gl-draw_ctrl-draw-btn.active {
  background: rgba(0, 116, 217, 0.8) !important;
}

/* Context menu styles */
.maplibre-context-menu {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 3D building styles */
.maplibregl-map .maplibregl-canvas-container canvas {
  outline: none;
}

/* Custom layer styles for tactical symbols */
.tactical-symbol {
  pointer-events: none;
  user-select: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .military-control-container {
    font-size: 12px;
  }

  .military-button {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    min-height: 28px !important;
    font-size: 9px !important;
  }

  .military-button svg {
    width: 12px !important;
    height: 12px !important;
  }

  .maplibregl-popup {
    max-width: 250px !important;
  }
}

@media (max-height: 600px) {
  .military-control-container {
    padding: 2px !important;
  }

  .military-button {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    min-height: 28px !important;
  }

  .military-button svg {
    width: 12px !important;
    height: 12px !important;
  }
}

/* Ensure controls don't overlap */
.map-controls-container {
  pointer-events: none;
}

.map-controls-container > * {
  pointer-events: auto;
}

/* Prevent controls from going off-screen */
.military-control-container {
  max-height: calc(100vh - 20px);
  overflow-y: auto;
  overflow-x: visible;
}

/* Compact layout adjustments */
.compact-layout .military-control-container {
  margin: 1px;
}

.compact-layout .military-button {
  margin: 0;
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .incident-marker,
  .response-marker,
  .cluster-marker {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .maplibregl-ctrl-group {
    background: rgba(26, 30, 35, 0.95) !important;
  }

  .military-control-container {
    background: rgba(26, 30, 35, 0.98);
  }
}
