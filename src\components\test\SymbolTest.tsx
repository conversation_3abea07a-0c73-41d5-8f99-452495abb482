import React from 'react';
import { IncidentType } from '@/types/incident';
import { tacticalSymbols } from '@/components/map/TacticalSymbols';

// Test component to verify tactical symbols are working
const SymbolTest: React.FC = () => {
  // Get SVG path for a specific tactical symbol (same function as in MapLibreMarkers)
  const getSvgPathForSymbol = (symbolType: string): string => {
    switch (symbolType) {
      // Original incident symbols - Enhanced NATO-style tactical symbols
      case 'physical_raid':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,12 L24,12 M8,20 L24,20" stroke="#fff" stroke-width="2.5" /><circle cx="16" cy="16" r="3" fill="#fff" />';

      case 'fire_raid':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,6 Q12,10 14,14 Q10,18 16,22 Q22,18 18,14 Q20,10 16,6" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

      case 'ambush':
        return '<polygon points="16,2 2,16 16,30 30,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,16 L24,16 M16,8 L16,24" stroke="#fff" stroke-width="2.5" /><circle cx="12" cy="12" r="2" fill="#fff" /><circle cx="20" cy="20" r="2" fill="#fff" />';

      case 'sniping':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16 M16,6 L16,26" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="6" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="2" fill="#fff" />';

      case 'demonstration':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="11" r="2.5" fill="#fff" /><path d="M8,20 Q16,25 24,20" stroke="#fff" stroke-width="2" fill="none" />';

      case 'ts_activity':
        return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="3" fill="#fff" /><path d="M16,8 L16,24 M8,16 L24,16" stroke="#fff" stroke-width="1.5" />';

      case 'ts_presence':
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.4" /><circle cx="16" cy="16" r="3" fill="#fff" />';

      default:
        return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><text x="16" y="20" font-size="12" text-anchor="middle" fill="#fff" font-weight="bold">?</text>';
    }
  };

  // Create tactical symbol SVG for incidents
  const createTacticalSymbolSvg = (type: IncidentType, size: number = 48) => {
    const symbol = tacticalSymbols[type] || tacticalSymbols[IncidentType.OTHER];
    const svgPath = getSvgPathForSymbol(symbol.symbol);

    return `
      <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
        <g fill="${symbol.color}" stroke="#000" stroke-width="1">
          ${svgPath}
        </g>
      </svg>
    `;
  };

  const testIncidentTypes = [
    IncidentType.PHYSICAL_RAID,
    IncidentType.FIRE_RAID,
    IncidentType.AMBUSH,
    IncidentType.SNIPING,
    IncidentType.DEMONSTRATION,
    IncidentType.TS_ACTIVITY,
    IncidentType.TS_PRESENCE,
    IncidentType.OTHER
  ];

  return (
    <div className="p-4 bg-military-panel text-military-white">
      <h2 className="text-xl font-bold mb-4">Tactical Symbol Test</h2>
      <div className="grid grid-cols-4 gap-4">
        {testIncidentTypes.map(type => {
          const symbol = tacticalSymbols[type];
          const svgIcon = createTacticalSymbolSvg(type, 48);
          const svgBase64 = btoa(svgIcon);
          const dataUrl = `data:image/svg+xml;base64,${svgBase64}`;

          return (
            <div key={type} className="flex flex-col items-center p-2 border border-military-border rounded">
              <img
                src={dataUrl}
                alt={type}
                width={48}
                height={48}
                className="mb-2"
              />
              <span className="text-xs text-center">{symbol?.description || type}</span>
              <span className="text-xs text-military-accent">{symbol?.color}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SymbolTest;
