import React, { useState, useCallback, useEffect } from 'react';
import { IncidentType, ActionType } from '@/types/incident';
import { tacticalSymbols, responseSymbols } from './TacticalSymbols';
import Button from '@/components/ui/Button';
import { 
  Palette, 
  Settings, 
  Save, 
  RotateCcw, 
  Download, 
  Upload,
  Plus,
  Trash2,
  Copy
} from 'lucide-react';

interface EnhancedSymbologyProps {
  onSymbolUpdate?: (type: string, config: any) => void;
}

interface SymbolConfig {
  color: string;
  description: string;
  shape: 'circle' | 'square' | 'diamond' | 'triangle' | 'pentagon' | 'hexagon';
  size: number;
  strokeWidth: number;
  strokeColor: string;
  text?: string;
  textColor: string;
  textSize: number;
  rotation: number;
  opacity: number;
}

const EnhancedSymbology: React.FC<EnhancedSymbologyProps> = ({ onSymbolUpdate }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'incidents' | 'responses'>('incidents');
  const [selectedSymbol, setSelectedSymbol] = useState<string | null>(null);
  const [symbolConfig, setSymbolConfig] = useState<SymbolConfig>({
    color: '#FF4136',
    description: '',
    shape: 'circle',
    size: 32,
    strokeWidth: 2,
    strokeColor: '#000000',
    text: '',
    textColor: '#FFFFFF',
    textSize: 12,
    rotation: 0,
    opacity: 1
  });
  const [customSymbols, setCustomSymbols] = useState<Record<string, SymbolConfig>>({});

  // Load custom symbols from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('customTacticalSymbols');
    if (saved) {
      try {
        setCustomSymbols(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load custom symbols:', error);
      }
    }
  }, []);

  // Save custom symbols to localStorage
  const saveCustomSymbols = useCallback((symbols: Record<string, SymbolConfig>) => {
    localStorage.setItem('customTacticalSymbols', JSON.stringify(symbols));
    setCustomSymbols(symbols);
  }, []);

  // Generate SVG for symbol preview
  const generateSymbolSVG = useCallback((config: SymbolConfig) => {
    const { color, shape, size, strokeWidth, strokeColor, text, textColor, textSize, rotation, opacity } = config;
    const center = size / 2;
    
    let shapeElement = '';
    switch (shape) {
      case 'circle':
        shapeElement = `<circle cx="${center}" cy="${center}" r="${center - strokeWidth}" fill="${color}" stroke="${strokeColor}" stroke-width="${strokeWidth}" opacity="${opacity}" />`;
        break;
      case 'square':
        shapeElement = `<rect x="${strokeWidth}" y="${strokeWidth}" width="${size - strokeWidth * 2}" height="${size - strokeWidth * 2}" fill="${color}" stroke="${strokeColor}" stroke-width="${strokeWidth}" opacity="${opacity}" />`;
        break;
      case 'diamond':
        shapeElement = `<polygon points="${center},${strokeWidth} ${size - strokeWidth},${center} ${center},${size - strokeWidth} ${strokeWidth},${center}" fill="${color}" stroke="${strokeColor}" stroke-width="${strokeWidth}" opacity="${opacity}" />`;
        break;
      case 'triangle':
        shapeElement = `<polygon points="${center},${strokeWidth} ${size - strokeWidth},${size - strokeWidth} ${strokeWidth},${size - strokeWidth}" fill="${color}" stroke="${strokeColor}" stroke-width="${strokeWidth}" opacity="${opacity}" />`;
        break;
      case 'pentagon':
        const pentagonPoints = [];
        for (let i = 0; i < 5; i++) {
          const angle = (i * 72 - 90) * Math.PI / 180;
          const x = center + (center - strokeWidth) * Math.cos(angle);
          const y = center + (center - strokeWidth) * Math.sin(angle);
          pentagonPoints.push(`${x},${y}`);
        }
        shapeElement = `<polygon points="${pentagonPoints.join(' ')}" fill="${color}" stroke="${strokeColor}" stroke-width="${strokeWidth}" opacity="${opacity}" />`;
        break;
      case 'hexagon':
        const hexagonPoints = [];
        for (let i = 0; i < 6; i++) {
          const angle = (i * 60) * Math.PI / 180;
          const x = center + (center - strokeWidth) * Math.cos(angle);
          const y = center + (center - strokeWidth) * Math.sin(angle);
          hexagonPoints.push(`${x},${y}`);
        }
        shapeElement = `<polygon points="${hexagonPoints.join(' ')}" fill="${color}" stroke="${strokeColor}" stroke-width="${strokeWidth}" opacity="${opacity}" />`;
        break;
    }

    const textElement = text ? 
      `<text x="${center}" y="${center + textSize / 3}" font-size="${textSize}" text-anchor="middle" fill="${textColor}" font-weight="bold">${text}</text>` : '';

    return `
      <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" transform="rotate(${rotation})">
        ${shapeElement}
        ${textElement}
      </svg>
    `;
  }, []);

  // Handle symbol selection
  const handleSymbolSelect = useCallback((symbolKey: string) => {
    setSelectedSymbol(symbolKey);
    
    // Load existing config or create default
    const existingSymbol = activeTab === 'incidents' ? tacticalSymbols[symbolKey as IncidentType] : responseSymbols[symbolKey as ActionType];
    const customConfig = customSymbols[symbolKey];
    
    if (customConfig) {
      setSymbolConfig(customConfig);
    } else if (existingSymbol) {
      setSymbolConfig({
        color: existingSymbol.color,
        description: existingSymbol.description,
        shape: 'circle',
        size: 32,
        strokeWidth: 2,
        strokeColor: '#000000',
        text: '',
        textColor: '#FFFFFF',
        textSize: 12,
        rotation: 0,
        opacity: 1
      });
    }
  }, [activeTab, customSymbols]);

  // Save symbol configuration
  const saveSymbolConfig = useCallback(() => {
    if (!selectedSymbol) return;
    
    const newCustomSymbols = {
      ...customSymbols,
      [selectedSymbol]: symbolConfig
    };
    
    saveCustomSymbols(newCustomSymbols);
    onSymbolUpdate?.(selectedSymbol, symbolConfig);
  }, [selectedSymbol, symbolConfig, customSymbols, saveCustomSymbols, onSymbolUpdate]);

  // Reset to default
  const resetToDefault = useCallback(() => {
    if (!selectedSymbol) return;
    
    const newCustomSymbols = { ...customSymbols };
    delete newCustomSymbols[selectedSymbol];
    saveCustomSymbols(newCustomSymbols);
    
    // Reload default config
    handleSymbolSelect(selectedSymbol);
  }, [selectedSymbol, customSymbols, saveCustomSymbols, handleSymbolSelect]);

  // Export symbols
  const exportSymbols = useCallback(() => {
    const dataStr = JSON.stringify(customSymbols, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'tactical-symbols.json';
    link.click();
    URL.revokeObjectURL(url);
  }, [customSymbols]);

  // Import symbols
  const importSymbols = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        saveCustomSymbols({ ...customSymbols, ...imported });
      } catch (error) {
        console.error('Failed to import symbols:', error);
      }
    };
    reader.readAsText(file);
  }, [customSymbols, saveCustomSymbols]);

  if (!isOpen) {
    return (
      <div className="absolute top-2 right-2 z-[1000]">
        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Enhanced Symbology"
          onClick={() => setIsOpen(true)}
        >
          <Palette size={14} />
        </Button>
      </div>
    );
  }

  return (
    <div className="absolute top-2 right-2 z-[1000] w-96">
      <div className="military-control-container rounded-md shadow-lg overflow-hidden">
        <div className="flex items-center justify-between px-2 py-1 border-b border-military-border">
          <span className="text-xs font-bold text-military-accent tracking-wider">ENHANCED SYMBOLOGY</span>
          <Button
            size="sm"
            variant="ghost"
            className="military-button p-1"
            onClick={() => setIsOpen(false)}
          >
            ×
          </Button>
        </div>

        <div className="p-2">
          {/* Tabs */}
          <div className="flex mb-2">
            <Button
              size="sm"
              variant="ghost"
              className={`military-button flex-1 ${activeTab === 'incidents' ? 'active' : ''}`}
              onClick={() => setActiveTab('incidents')}
            >
              Incidents
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button flex-1 ${activeTab === 'responses' ? 'active' : ''}`}
              onClick={() => setActiveTab('responses')}
            >
              Responses
            </Button>
          </div>

          {/* Symbol list */}
          <div className="mb-2 max-h-32 overflow-y-auto border border-military-border">
            {Object.entries(activeTab === 'incidents' ? tacticalSymbols : responseSymbols).map(([key, symbol]) => (
              <div
                key={key}
                className={`p-1 cursor-pointer hover:bg-military-navy ${selectedSymbol === key ? 'bg-military-accent' : ''}`}
                onClick={() => handleSymbolSelect(key)}
              >
                <span className="text-xs text-military-white">{symbol.description}</span>
              </div>
            ))}
          </div>

          {/* Symbol editor */}
          {selectedSymbol && (
            <div className="space-y-2">
              {/* Preview */}
              <div className="flex items-center justify-center p-2 border border-military-border bg-military-navy">
                <div dangerouslySetInnerHTML={{ __html: generateSymbolSVG(symbolConfig) }} />
              </div>

              {/* Controls */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <label className="text-military-white">Color</label>
                  <input
                    type="color"
                    value={symbolConfig.color}
                    onChange={(e) => setSymbolConfig(prev => ({ ...prev, color: e.target.value }))}
                    className="w-full h-6"
                  />
                </div>
                <div>
                  <label className="text-military-white">Shape</label>
                  <select
                    value={symbolConfig.shape}
                    onChange={(e) => setSymbolConfig(prev => ({ ...prev, shape: e.target.value as any }))}
                    className="w-full bg-military-navy border border-military-border text-military-white"
                  >
                    <option value="circle">Circle</option>
                    <option value="square">Square</option>
                    <option value="diamond">Diamond</option>
                    <option value="triangle">Triangle</option>
                    <option value="pentagon">Pentagon</option>
                    <option value="hexagon">Hexagon</option>
                  </select>
                </div>
                <div>
                  <label className="text-military-white">Size</label>
                  <input
                    type="range"
                    min="16"
                    max="64"
                    value={symbolConfig.size}
                    onChange={(e) => setSymbolConfig(prev => ({ ...prev, size: parseInt(e.target.value) }))}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-military-white">Text</label>
                  <input
                    type="text"
                    value={symbolConfig.text}
                    onChange={(e) => setSymbolConfig(prev => ({ ...prev, text: e.target.value }))}
                    className="w-full bg-military-navy border border-military-border text-military-white px-1"
                    placeholder="Symbol text"
                  />
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex gap-1">
                <Button size="sm" variant="ghost" className="military-button flex-1" onClick={saveSymbolConfig}>
                  <Save size={12} />
                </Button>
                <Button size="sm" variant="ghost" className="military-button flex-1" onClick={resetToDefault}>
                  <RotateCcw size={12} />
                </Button>
                <Button size="sm" variant="ghost" className="military-button flex-1" onClick={exportSymbols}>
                  <Download size={12} />
                </Button>
                <label className="flex-1">
                  <Button size="sm" variant="ghost" className="military-button w-full">
                    <Upload size={12} />
                  </Button>
                  <input type="file" accept=".json" onChange={importSymbols} className="hidden" />
                </label>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedSymbology;
