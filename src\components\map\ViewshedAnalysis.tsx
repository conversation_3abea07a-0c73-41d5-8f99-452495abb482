import React, { useState, useEffect, useRef, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import Button from '@/components/ui/Button';
import { Eye, Target, Crosshair, Settings, Trash2, Save } from 'lucide-react';

interface ViewshedAnalysisProps {
  map: maplibregl.Map | null;
}

interface ViewshedPoint {
  id: string;
  coordinates: [number, number];
  elevation: number;
  viewRadius: number;
  viewAngle: number;
  direction: number;
}

const ViewshedAnalysis: React.FC<ViewshedAnalysisProps> = ({ map }) => {
  const [isActive, setIsActive] = useState(false);
  const [viewshedPoints, setViewshedPoints] = useState<ViewshedPoint[]>([]);
  const [selectedPoint, setSelectedPoint] = useState<string | null>(null);
  const [settings, setSettings] = useState({
    defaultRadius: 1000, // meters
    defaultAngle: 360, // degrees
    observerHeight: 1.7, // meters
    targetHeight: 1.7, // meters
    terrainSampling: 50 // meters between samples
  });
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  
  const markersRef = useRef<Map<string, maplibregl.Marker>>(new Map());
  const viewshedLayersRef = useRef<Set<string>>(new Set());

  // Calculate viewshed using simple line-of-sight algorithm
  const calculateViewshed = useCallback(async (point: ViewshedPoint) => {
    if (!map) return;

    const { coordinates, elevation, viewRadius, viewAngle, direction } = point;
    const [lng, lat] = coordinates;

    // Create a grid of points to check visibility
    const gridSize = 20; // 20x20 grid
    const stepLat = (viewRadius / 111320) / gridSize; // Convert meters to degrees
    const stepLng = stepLat / Math.cos(lat * Math.PI / 180);

    const visiblePoints: [number, number][] = [];
    const hiddenPoints: [number, number][] = [];

    // Calculate angle range
    const startAngle = direction - viewAngle / 2;
    const endAngle = direction + viewAngle / 2;

    for (let i = -gridSize; i <= gridSize; i++) {
      for (let j = -gridSize; j <= gridSize; j++) {
        const targetLat = lat + i * stepLat;
        const targetLng = lng + j * stepLng;
        
        // Calculate distance
        const distance = Math.sqrt(
          Math.pow((targetLat - lat) * 111320, 2) + 
          Math.pow((targetLng - lng) * 111320 * Math.cos(lat * Math.PI / 180), 2)
        );

        if (distance > viewRadius || distance < 10) continue;

        // Calculate bearing
        const bearing = Math.atan2(
          (targetLng - lng) * Math.cos(lat * Math.PI / 180),
          targetLat - lat
        ) * 180 / Math.PI;

        // Check if within view angle
        if (viewAngle < 360) {
          let normalizedBearing = bearing;
          if (normalizedBearing < 0) normalizedBearing += 360;
          
          let normalizedStart = startAngle;
          let normalizedEnd = endAngle;
          
          if (normalizedStart < 0) {
            normalizedStart += 360;
            normalizedEnd += 360;
          }
          
          if (normalizedEnd > 360) {
            // Handle wrap-around
            if (!(normalizedBearing >= normalizedStart || normalizedBearing <= normalizedEnd - 360)) {
              continue;
            }
          } else {
            if (!(normalizedBearing >= normalizedStart && normalizedBearing <= normalizedEnd)) {
              continue;
            }
          }
        }

        // Simple line-of-sight calculation (assumes flat terrain for now)
        // In a real implementation, this would use DEM data
        const isVisible = true; // Simplified - assume all points are visible

        if (isVisible) {
          visiblePoints.push([targetLng, targetLat]);
        } else {
          hiddenPoints.push([targetLng, targetLat]);
        }
      }
    }

    // Add viewshed visualization to map
    addViewshedToMap(point.id, coordinates, visiblePoints, hiddenPoints, viewAngle, direction);
  }, [map]);

  // Add viewshed visualization to map
  const addViewshedToMap = useCallback((
    pointId: string,
    center: [number, number],
    visiblePoints: [number, number][],
    hiddenPoints: [number, number][],
    viewAngle: number,
    direction: number
  ) => {
    if (!map) return;

    const sourceId = `viewshed-${pointId}`;
    const visibleLayerId = `viewshed-visible-${pointId}`;
    const hiddenLayerId = `viewshed-hidden-${pointId}`;
    const directionLayerId = `viewshed-direction-${pointId}`;

    // Remove existing layers
    if (map.getLayer(visibleLayerId)) map.removeLayer(visibleLayerId);
    if (map.getLayer(hiddenLayerId)) map.removeLayer(hiddenLayerId);
    if (map.getLayer(directionLayerId)) map.removeLayer(directionLayerId);
    if (map.getSource(sourceId)) map.removeSource(sourceId);

    // Create GeoJSON for visible and hidden areas
    const visibleFeatures = visiblePoints.map(coord => ({
      type: 'Feature' as const,
      geometry: {
        type: 'Point' as const,
        coordinates: coord
      },
      properties: {}
    }));

    const hiddenFeatures = hiddenPoints.map(coord => ({
      type: 'Feature' as const,
      geometry: {
        type: 'Point' as const,
        coordinates: coord
      },
      properties: {}
    }));

    // Add direction indicator if not 360 degrees
    let directionFeature = null;
    if (viewAngle < 360) {
      const radius = 500; // meters
      const startAngle = direction - viewAngle / 2;
      const endAngle = direction + viewAngle / 2;
      
      const arcPoints = [];
      for (let angle = startAngle; angle <= endAngle; angle += 5) {
        const radians = angle * Math.PI / 180;
        const lat = center[1] + (radius / 111320) * Math.cos(radians);
        const lng = center[0] + (radius / 111320) * Math.sin(radians) / Math.cos(center[1] * Math.PI / 180);
        arcPoints.push([lng, lat]);
      }
      
      directionFeature = {
        type: 'Feature' as const,
        geometry: {
          type: 'Polygon' as const,
          coordinates: [[center, ...arcPoints, center]]
        },
        properties: {}
      };
    }

    // Add source
    map.addSource(sourceId, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: [...visibleFeatures, ...hiddenFeatures, ...(directionFeature ? [directionFeature] : [])]
      }
    });

    // Add visible area layer
    map.addLayer({
      id: visibleLayerId,
      type: 'circle',
      source: sourceId,
      filter: ['==', ['geometry-type'], 'Point'],
      paint: {
        'circle-radius': 3,
        'circle-color': '#00ff00',
        'circle-opacity': 0.6
      }
    });

    // Add direction indicator layer
    if (directionFeature) {
      map.addLayer({
        id: directionLayerId,
        type: 'fill',
        source: sourceId,
        filter: ['==', ['geometry-type'], 'Polygon'],
        paint: {
          'fill-color': '#ffff00',
          'fill-opacity': 0.2
        }
      });
    }

    viewshedLayersRef.current.add(visibleLayerId);
    viewshedLayersRef.current.add(hiddenLayerId);
    viewshedLayersRef.current.add(directionLayerId);
  }, [map]);

  // Handle map click to add viewshed point
  const handleMapClick = useCallback((e: maplibregl.MapMouseEvent) => {
    if (!isActive || !map) return;

    const newPoint: ViewshedPoint = {
      id: `viewshed-${Date.now()}`,
      coordinates: [e.lngLat.lng, e.lngLat.lat],
      elevation: 0, // Would be fetched from DEM in real implementation
      viewRadius: settings.defaultRadius,
      viewAngle: settings.defaultAngle,
      direction: 0
    };

    // Add marker
    const marker = new maplibregl.Marker({ color: '#ff0000' })
      .setLngLat(newPoint.coordinates)
      .addTo(map);

    markersRef.current.set(newPoint.id, marker);
    setViewshedPoints(prev => [...prev, newPoint]);
    calculateViewshed(newPoint);
  }, [isActive, map, settings, calculateViewshed]);

  // Setup map click handler
  useEffect(() => {
    if (!map) return;

    if (isActive) {
      map.on('click', handleMapClick);
      map.getCanvas().style.cursor = 'crosshair';
    } else {
      map.off('click', handleMapClick);
      map.getCanvas().style.cursor = '';
    }

    return () => {
      map.off('click', handleMapClick);
      map.getCanvas().style.cursor = '';
    };
  }, [map, isActive, handleMapClick]);

  // Clear all viewsheds
  const clearViewsheds = useCallback(() => {
    if (!map) return;

    // Remove all markers
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current.clear();

    // Remove all layers
    viewshedLayersRef.current.forEach(layerId => {
      if (map.getLayer(layerId)) map.removeLayer(layerId);
    });
    viewshedLayersRef.current.clear();

    // Remove all sources
    viewshedPoints.forEach(point => {
      const sourceId = `viewshed-${point.id}`;
      if (map.getSource(sourceId)) map.removeSource(sourceId);
    });

    setViewshedPoints([]);
    setSelectedPoint(null);
  }, [map, viewshedPoints]);

  return (
    <div className="absolute top-2 right-80 z-[1000] flex flex-col gap-2">
      {/* Main controls */}
      <div className="military-control-container rounded-md shadow-lg overflow-hidden">
        <div className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider border-b border-military-border">
          VIEWSHED ANALYSIS
        </div>
        <div className="flex flex-col gap-1 p-1">
          <Button
            size="sm"
            variant="ghost"
            className={`military-button ${isActive ? 'active' : ''}`}
            title="Toggle Viewshed Mode"
            onClick={() => setIsActive(!isActive)}
          >
            <Eye size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Settings"
            onClick={() => setIsSettingsOpen(!isSettingsOpen)}
          >
            <Settings size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Clear All"
            onClick={clearViewsheds}
          >
            <Trash2 size={14} />
          </Button>
        </div>
      </div>

      {/* Settings panel */}
      {isSettingsOpen && (
        <div className="military-control-container rounded-md shadow-lg overflow-hidden min-w-[200px]">
          <div className="px-2 py-1 text-xs font-bold text-military-accent tracking-wider border-b border-military-border">
            VIEWSHED SETTINGS
          </div>
          <div className="p-2 space-y-2">
            <div>
              <label className="text-xs text-military-white">Radius (m)</label>
              <input
                type="number"
                value={settings.defaultRadius}
                onChange={(e) => setSettings(prev => ({ ...prev, defaultRadius: parseInt(e.target.value) }))}
                className="w-full text-xs bg-military-navy border border-military-border text-military-white px-1 py-0.5"
              />
            </div>
            <div>
              <label className="text-xs text-military-white">View Angle (°)</label>
              <input
                type="number"
                value={settings.defaultAngle}
                onChange={(e) => setSettings(prev => ({ ...prev, defaultAngle: parseInt(e.target.value) }))}
                className="w-full text-xs bg-military-navy border border-military-border text-military-white px-1 py-0.5"
              />
            </div>
            <div>
              <label className="text-xs text-military-white">Observer Height (m)</label>
              <input
                type="number"
                step="0.1"
                value={settings.observerHeight}
                onChange={(e) => setSettings(prev => ({ ...prev, observerHeight: parseFloat(e.target.value) }))}
                className="w-full text-xs bg-military-navy border border-military-border text-military-white px-1 py-0.5"
              />
            </div>
          </div>
        </div>
      )}

      {/* Active viewshed info */}
      {isActive && (
        <div className="military-control-container rounded-md shadow-lg overflow-hidden">
          <div className="p-2 text-xs text-military-white">
            <div className="text-military-accent font-bold mb-1">INSTRUCTIONS</div>
            <div>Click on map to place viewshed observer</div>
            <div className="mt-1 text-military-accent">Points: {viewshedPoints.length}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewshedAnalysis;
