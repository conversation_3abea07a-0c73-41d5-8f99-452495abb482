import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell,
  <PERSON><PERSON>hart, Line, <PERSON>Axis, <PERSON>Axis, CartesianGrid,
  <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer
} from 'recharts';
import { format, parseISO } from 'date-fns';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import {
  Pie<PERSON><PERSON> as PieChartIcon,
  <PERSON><PERSON><PERSON> as Bar<PERSON><PERSON>Icon,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  Filter,
  RefreshCw
} from 'lucide-react';
import { useIncidentStore } from '@/store/incidentStore';
import { useFilterStore, ChartFilter } from '@/store/filterStore';
import {
  IncidentType,
  IncidentSeverity,
  IncidentStatus
} from '@/types/incident';
import { incidentTypeColors } from '@/services/map/mapService';

// Helper function to get color for severity
const getSeverityColor = (severity: IncidentSeverity) => {
  switch (severity) {
    case IncidentSeverity.CRITICAL: return '#FF4136';
    case IncidentSeverity.HIGH: return '#FF851B';
    case IncidentSeverity.MEDIUM: return '#FFDC00';
    case IncidentSeverity.LOW: return '#2ECC40';
    default: return '#AAAAAA';
  }
};

// Helper function to get color for status
const getStatusColor = (status: IncidentStatus) => {
  switch (status) {
    case IncidentStatus.REPORTED: return '#FF851B';
    case IncidentStatus.IN_PROGRESS: return '#0074D9';
    case IncidentStatus.RESOLVED: return '#2ECC40';
    case IncidentStatus.CLOSED: return '#AAAAAA';
    default: return '#AAAAAA';
  }
};

// Type Distribution Chart
const TypeDistributionChart: React.FC = () => {
  const { statistics } = useIncidentStore();
  const { addChartFilter, chartFilters } = useFilterStore();

  if (!statistics) return null;

  // Convert type data to chart format
  const data = Object.entries(statistics.byType)
    .map(([type, count]) => ({
      name: type.replace('TS_', ''),
      value: count,
      fullType: type
    }))
    .filter(item => item.value > 0);

  // Check if a type is already filtered
  const isTypeFiltered = (type: string) => {
    return chartFilters.some(f => f.source === 'type' && f.value === type);
  };

  return (
    <Card
      title={
        <div className="flex items-center">
          <PieChartIcon size={16} className="mr-2" />
          <span>INCIDENT TYPES</span>
        </div>
      }
      variant="military"
      className="h-full"
    >
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              onClick={(data) => {
                // Add chart filter when segment is clicked
                addChartFilter({
                  source: 'type',
                  value: data.fullType,
                  label: `Type: ${data.name}`
                });

                // Prevent event propagation to avoid map zooming
                if (window.event) {
                  window.event.stopPropagation();
                }
              }}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={incidentTypeColors[entry.fullType as IncidentType]}
                  stroke={isTypeFiltered(entry.fullType) ? '#fff' : '#000'}
                  strokeWidth={isTypeFiltered(entry.fullType) ? 2 : 1}
                />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [`${value} incidents`, 'Count']}
              contentStyle={{
                backgroundColor: '#1A2237',
                border: '1px solid #2D3748',
                borderRadius: '4px'
              }}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
      <div className="text-xs text-center mt-2 text-gray-400">
        Click on segments to filter by incident type
      </div>
    </Card>
  );
};

// Severity Distribution Chart
const SeverityDistributionChart: React.FC = () => {
  const { statistics } = useIncidentStore();
  const { addChartFilter, chartFilters } = useFilterStore();

  if (!statistics) return null;

  // Convert severity data to chart format
  const data = Object.entries(statistics.bySeverity)
    .map(([severity, count]) => ({
      name: severity,
      value: count,
      fullSeverity: severity
    }))
    .filter(item => item.value > 0);

  // Check if a severity is already filtered
  const isSeverityFiltered = (severity: string) => {
    return chartFilters.some(f => f.source === 'severity' && f.value === severity);
  };

  return (
    <Card
      title={
        <div className="flex items-center">
          <BarChartIcon size={16} className="mr-2" />
          <span>SEVERITY LEVELS</span>
        </div>
      }
      variant="military"
      className="h-full"
    >
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#2D3748" />
            <XAxis dataKey="name" tick={{ fill: '#A0AEC0' }} />
            <YAxis tick={{ fill: '#A0AEC0' }} />
            <Tooltip
              formatter={(value: number) => [`${value} incidents`, 'Count']}
              contentStyle={{
                backgroundColor: '#1A2237',
                border: '1px solid #2D3748',
                borderRadius: '4px'
              }}
            />
            <Bar
              dataKey="value"
              onClick={(data) => {
                // Add chart filter when bar is clicked
                addChartFilter({
                  source: 'severity',
                  value: data.fullSeverity,
                  label: `Severity: ${data.name}`
                });

                // Prevent event propagation to avoid map zooming
                if (window.event) {
                  window.event.stopPropagation();
                }
              }}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={getSeverityColor(entry.fullSeverity as IncidentSeverity)}
                  stroke={isSeverityFiltered(entry.fullSeverity) ? '#fff' : '#000'}
                  strokeWidth={isSeverityFiltered(entry.fullSeverity) ? 2 : 1}
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
      <div className="text-xs text-center mt-2 text-gray-400">
        Click on bars to filter by severity level
      </div>
    </Card>
  );
};

// Status Distribution Chart
const StatusDistributionChart: React.FC = () => {
  const { statistics } = useIncidentStore();
  const { addChartFilter, chartFilters } = useFilterStore();

  if (!statistics) return null;

  // Convert status data to chart format
  const data = Object.entries(statistics.byStatus)
    .map(([status, count]) => ({
      name: status.replace('_', ' '),
      value: count,
      fullStatus: status
    }))
    .filter(item => item.value > 0);

  // Check if a status is already filtered
  const isStatusFiltered = (status: string) => {
    return chartFilters.some(f => f.source === 'status' && f.value === status);
  };

  return (
    <Card
      title={
        <div className="flex items-center">
          <PieChartIcon size={16} className="mr-2" />
          <span>INCIDENT STATUS</span>
        </div>
      }
      variant="military"
      className="h-full"
    >
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              onClick={(data) => {
                // Add chart filter when segment is clicked
                addChartFilter({
                  source: 'status',
                  value: data.fullStatus,
                  label: `Status: ${data.name}`
                });

                // Prevent event propagation to avoid map zooming
                if (window.event) {
                  window.event.stopPropagation();
                }
              }}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={getStatusColor(entry.fullStatus as IncidentStatus)}
                  stroke={isStatusFiltered(entry.fullStatus) ? '#fff' : '#000'}
                  strokeWidth={isStatusFiltered(entry.fullStatus) ? 2 : 1}
                />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [`${value} incidents`, 'Count']}
              contentStyle={{
                backgroundColor: '#1A2237',
                border: '1px solid #2D3748',
                borderRadius: '4px'
              }}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
      <div className="text-xs text-center mt-2 text-gray-400">
        Click on segments to filter by incident status
      </div>
    </Card>
  );
};

// Timeline Chart
const TimelineChart: React.FC = () => {
  const { statistics } = useIncidentStore();
  const { addChartFilter, chartFilters, dateRange } = useFilterStore();
  const [timeScale, setTimeScale] = useState<'day' | 'month'>('day');

  if (!statistics) return null;

  // Get the appropriate data based on time scale
  const data = timeScale === 'day'
    ? statistics.byDay.map(item => ({
        date: item.date,
        count: item.count,
        formattedDate: format(parseISO(item.date), 'MMM dd')
      }))
    : statistics.byMonth.map(item => ({
        date: item.month,
        count: item.count,
        formattedDate: item.month
      }));

  // Check if a date is within the filtered range
  const isDateInRange = (dateStr: string) => {
    if (!dateRange.startDate || !dateRange.endDate) return false;

    const date = timeScale === 'day'
      ? parseISO(dateStr)
      : new Date(dateStr); // For month format "MMM yyyy"

    return date >= dateRange.startDate && date <= dateRange.endDate;
  };

  return (
    <Card
      title={
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <LineChartIcon size={16} className="mr-2" />
            <span>INCIDENT TIMELINE</span>
          </div>
          <div className="flex space-x-1">
            <Button
              size="xs"
              variant="ghost"
              className={`military-button ${timeScale === 'day' ? 'active' : ''}`}
              onClick={() => setTimeScale('day')}
            >
              Daily
            </Button>
            <Button
              size="xs"
              variant="ghost"
              className={`military-button ${timeScale === 'month' ? 'active' : ''}`}
              onClick={() => setTimeScale('month')}
            >
              Monthly
            </Button>
          </div>
        </div>
      }
      variant="military"
      className="h-full"
    >
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#2D3748" />
            <XAxis
              dataKey="formattedDate"
              tick={{ fill: '#A0AEC0' }}
              interval={timeScale === 'day' ? 2 : 0}
              angle={-45}
              textAnchor="end"
              height={50}
            />
            <YAxis tick={{ fill: '#A0AEC0' }} />
            <Tooltip
              formatter={(value: number) => [`${value} incidents`, 'Count']}
              labelFormatter={(label) => `Date: ${label}`}
              contentStyle={{
                backgroundColor: '#1A2237',
                border: '1px solid #2D3748',
                borderRadius: '4px'
              }}
            />
            <Line
              type="monotone"
              dataKey="count"
              stroke="#8884d8"
              activeDot={{
                r: 8,
                onClick: (data: any) => {
                  // Get the original data point
                  const point = data.payload;

                  // Add chart filter when dot is clicked
                  addChartFilter({
                    source: 'timeline',
                    value: timeScale === 'day' ? parseISO(point.date) : new Date(point.date),
                    label: `Date: ${point.formattedDate}`
                  });

                  // Prevent event propagation to avoid map zooming
                  if (window.event) {
                    window.event.stopPropagation();
                  }
                }
              }}
              dot={{
                stroke: '#8884d8',
                strokeWidth: 2,
                r: 4,
                fill: (data: any) => isDateInRange(data.date) ? '#fff' : '#8884d8'
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div className="text-xs text-center mt-2 text-gray-400">
        Click on data points to filter by date
      </div>
    </Card>
  );
};

// Combined interactive charts component
const InteractiveCharts: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <TypeDistributionChart />
      <SeverityDistributionChart />
      <StatusDistributionChart />
      <TimelineChart />
    </div>
  );
};

export default InteractiveCharts;
