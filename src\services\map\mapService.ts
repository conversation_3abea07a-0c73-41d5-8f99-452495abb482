import { Incident, IncidentType } from '@/types/incident';

// Colors for different incident types
export const incidentTypeColors: Record<IncidentType, string> = {
  [IncidentType.TS_ACTIVITY]: '#FF4136',
  [IncidentType.TS_INFIL]: '#0074D9',
  [IncidentType.TS_PRESENCE]: '#FF851B',
  [IncidentType.TS_MOV]: '#FFDC00',
  [IncidentType.TS_TASKEEL]: '#7FDBFF',
  [IncidentType.TS_SB]: '#B10DC9',
  [IncidentType.TS_EXTORTION]: '#85144b',
  [IncidentType.TS_SUSPECT]: '#39CCCC',
  [IncidentType.TS_MEETING]: '#01FF70',
  [IncidentType.TS_SEEN]: '#F012BE',
  [IncidentType.TS_TGT_KILLING]: '#FF4136',
  [IncidentType.TS_JIRGA]: '#FFDC00',
  [IncidentType.OTHER]: '#AAAAAA'
};

// This would be replaced with actual offline MBTiles implementation
export const initializeOfflineMaps = async () => {
  console.log('Initializing offline maps...');
  // In a real implementation, this would load MBTiles files
  return { success: true };
};

// Function to create a GeoJSON feature collection from incidents
export const createGeoJsonFromIncidents = (incidents: Incident[]) => {
  return {
    type: 'FeatureCollection',
    features: incidents.map(incident => ({
      type: 'Feature',
      properties: {
        id: incident.id,
        title: incident.title,
        type: incident.type,
        action: incident.action,
        severity: incident.severity,
        status: incident.status,
        reportedAt: incident.reportedAt,
        color: incidentTypeColors[incident.type]
      },
      geometry: {
        type: 'Point',
        coordinates: [incident.location.longitude, incident.location.latitude]
      }
    }))
  };
};

// Function to create a heatmap data from incidents
export const createHeatmapData = (incidents: Incident[]) => {
  return incidents.map(incident => {
    // Weight based on severity
    let weight = 1;
    switch (incident.severity) {
      case 'LOW': weight = 1; break;
      case 'MEDIUM': weight = 2; break;
      case 'HIGH': weight = 3; break;
      case 'CRITICAL': weight = 4; break;
    }
    
    return {
      lat: incident.location.latitude,
      lng: incident.location.longitude,
      weight
    };
  });
};