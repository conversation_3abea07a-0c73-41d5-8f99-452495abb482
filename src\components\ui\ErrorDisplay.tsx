import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, X, ChevronDown, ChevronUp, RefreshCw } from 'lucide-react';
import { errorService, LogEntry, LogLevel, ErrorCategory } from '@/services/logging/errorService';

interface ErrorDisplayProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  autoHide?: boolean;
  autoHideDelay?: number;
  maxErrors?: number;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  position = 'bottom-right',
  autoHide = true,
  autoHideDelay = 5000,
  maxErrors = 3
}) => {
  const [errors, setErrors] = useState<LogEntry[]>([]);
  const [expanded, setExpanded] = useState(false);
  const [visible, setVisible] = useState(false);

  // Position classes
  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  // Set up error callback
  useEffect(() => {
    const handleError = (error: LogEntry) => {
      setErrors(prev => {
        // Add new error to the beginning of the array
        const newErrors = [error, ...prev];
        // Limit to maxErrors
        return newErrors.slice(0, maxErrors);
      });
      setVisible(true);

      // Auto-hide after delay if enabled
      if (autoHide) {
        const timer = setTimeout(() => {
          setVisible(false);
        }, autoHideDelay);
        return () => clearTimeout(timer);
      }
    };

    // Register error callback
    errorService.setErrorCallback(handleError);

    // Clean up
    return () => {
      // Remove callback (not implemented in the service, but would be good to add)
    };
  }, [autoHide, autoHideDelay, maxErrors]);

  // No errors to display
  if (errors.length === 0) {
    return null;
  }

  // Get severity color
  const getSeverityColor = (level: LogLevel) => {
    switch (level) {
      case LogLevel.CRITICAL:
        return 'bg-military-red';
      case LogLevel.ERROR:
        return 'bg-military-red bg-opacity-80';
      case LogLevel.WARN:
        return 'bg-military-amber';
      case LogLevel.INFO:
        return 'bg-military-navy';
      default:
        return 'bg-military-panel';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: ErrorCategory) => {
    switch (category) {
      case ErrorCategory.MAP:
        return '🗺️';
      case ErrorCategory.DATABASE:
        return '💾';
      case ErrorCategory.NETWORK:
        return '🌐';
      case ErrorCategory.UI:
        return '🖥️';
      case ErrorCategory.AUTHENTICATION:
        return '🔒';
      default:
        return '⚠️';
    }
  };

  // Clear all errors
  const clearErrors = () => {
    setErrors([]);
    setVisible(false);
  };

  // If not visible, show only a small indicator
  if (!visible && !expanded) {
    return (
      <div 
        className={`fixed ${positionClasses[position]} z-[2000] military-container p-2 bg-military-panel cursor-pointer`}
        onClick={() => setVisible(true)}
        style={{ clipPath: 'polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px))' }}
      >
        <div className="flex items-center">
          <AlertTriangle size={16} className="text-military-red mr-2" />
          <span className="text-xs font-military-body text-military-white">
            {errors.length} {errors.length === 1 ? 'ERROR' : 'ERRORS'}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`fixed ${positionClasses[position]} z-[2000] military-container bg-military-black border border-military-border max-w-md w-full`}
      style={{ clipPath: 'polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px))' }}
    >
      {/* Header */}
      <div className="bg-military-navy p-2 flex items-center justify-between border-b border-military-border">
        <div className="flex items-center">
          <AlertTriangle size={16} className="text-military-red mr-2" />
          <h3 className="text-sm font-military-heading text-military-white uppercase tracking-wider">
            SYSTEM ALERTS ({errors.length})
          </h3>
        </div>
        <div className="flex items-center space-x-1">
          <button 
            className="military-btn p-1"
            onClick={() => setExpanded(!expanded)}
            title={expanded ? "Collapse" : "Expand"}
          >
            {expanded ? <ChevronDown size={14} /> : <ChevronUp size={14} />}
          </button>
          <button 
            className="military-btn p-1"
            onClick={clearErrors}
            title="Clear all errors"
          >
            <RefreshCw size={14} />
          </button>
          <button 
            className="military-btn p-1"
            onClick={() => setVisible(false)}
            title="Close"
          >
            <X size={14} />
          </button>
        </div>
      </div>

      {/* Error list */}
      <div className={`overflow-y-auto ${expanded ? 'max-h-96' : 'max-h-40'}`}>
        {errors.map((error, index) => (
          <div 
            key={`${error.timestamp}-${index}`}
            className={`p-2 border-b border-military-border ${getSeverityColor(error.level)}`}
          >
            <div className="flex items-start">
              <div className="mr-2 text-lg">{getCategoryIcon(error.category || ErrorCategory.UNKNOWN)}</div>
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <h4 className="text-xs font-military-body text-military-white uppercase font-bold">
                    {error.category} {error.level}
                  </h4>
                  <span className="text-xs font-mono text-military-white opacity-70">
                    {new Date(error.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <p className="text-sm text-military-white mt-1">{error.message}</p>
                
                {/* Show details if expanded */}
                {expanded && error.details && (
                  <div className="mt-2 p-2 bg-military-black bg-opacity-30 text-xs font-mono text-military-white overflow-x-auto">
                    {typeof error.details === 'object' 
                      ? JSON.stringify(error.details, null, 2)
                      : String(error.details)
                    }
                  </div>
                )}
                
                {/* Show stack trace if expanded */}
                {expanded && error.stack && (
                  <div className="mt-2 p-2 bg-military-black bg-opacity-30 text-xs font-mono text-military-white overflow-x-auto">
                    {error.stack}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ErrorDisplay;
