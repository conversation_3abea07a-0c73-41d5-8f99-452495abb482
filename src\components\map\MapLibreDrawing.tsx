import React, { useEffect, useRef } from 'react';
import maplibregl from 'maplibre-gl';
import MapboxDraw from '@mapbox/mapbox-gl-draw';
import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';

interface MapLibreDrawingProps {
  map: maplibregl.Map | null;
}

const MapLibreDrawing: React.FC<MapLibreDrawingProps> = ({ map }) => {
  const drawRef = useRef<MapboxDraw | null>(null);

  useEffect(() => {
    if (!map) return;

    // Check if draw control already exists
    if (drawRef.current) {
      return;
    }

    // Initialize drawing control
    drawRef.current = new MapboxDraw({
      displayControlsDefault: false,
      controls: {
        point: true,
        line_string: true,
        polygon: true,
        trash: true
      },
      styles: [
        // Point style
        {
          id: 'gl-draw-point',
          type: 'circle',
          filter: ['all', ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
          paint: {
            'circle-radius': 6,
            'circle-color': '#FF4136',
            'circle-stroke-width': 2,
            'circle-stroke-color': '#FFFFFF'
          }
        },
        // Line style
        {
          id: 'gl-draw-line',
          type: 'line',
          filter: ['all', ['==', '$type', 'LineString'], ['!=', 'mode', 'static']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round'
          },
          paint: {
            'line-color': '#FF4136',
            'line-width': 3
          }
        },
        // Polygon fill
        {
          id: 'gl-draw-polygon-fill',
          type: 'fill',
          filter: ['all', ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
          paint: {
            'fill-color': '#FF4136',
            'fill-opacity': 0.2
          }
        },
        // Polygon stroke
        {
          id: 'gl-draw-polygon-stroke',
          type: 'line',
          filter: ['all', ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round'
          },
          paint: {
            'line-color': '#FF4136',
            'line-width': 3
          }
        },
        // Vertex points
        {
          id: 'gl-draw-polygon-and-line-vertex-halo-active',
          type: 'circle',
          filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
          paint: {
            'circle-radius': 5,
            'circle-color': '#FFFFFF'
          }
        },
        {
          id: 'gl-draw-polygon-and-line-vertex-active',
          type: 'circle',
          filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point'], ['!=', 'mode', 'static']],
          paint: {
            'circle-radius': 3,
            'circle-color': '#FF4136'
          }
        }
      ]
    });

    // Add drawing control to map
    map.addControl(drawRef.current, 'top-left');

    // Event listeners for drawing
    const handleDrawCreate = (e: any) => {
      console.log('Feature created:', e.features);
      // You can add custom logic here to handle created features
    };

    const handleDrawUpdate = (e: any) => {
      console.log('Feature updated:', e.features);
    };

    const handleDrawDelete = (e: any) => {
      console.log('Feature deleted:', e.features);
    };

    map.on('draw.create', handleDrawCreate);
    map.on('draw.update', handleDrawUpdate);
    map.on('draw.delete', handleDrawDelete);

    // Listen for custom drawing events from toolbar
    const handleStartDraw = (event: CustomEvent) => {
      if (!drawRef.current) return;

      const { mode } = event.detail;

      switch (mode) {
        case 'draw_point':
          drawRef.current.changeMode('draw_point');
          break;
        case 'draw_line_string':
          drawRef.current.changeMode('draw_line_string');
          break;
        case 'draw_polygon':
          drawRef.current.changeMode('draw_polygon');
          break;
        case 'draw_circle':
          // MapboxDraw doesn't have built-in circle mode, so we'll use a custom implementation
          drawCircle();
          break;
        default:
          drawRef.current.changeMode('simple_select');
      }
    };

    window.addEventListener('maplibre:startDraw', handleStartDraw as EventListener);

    return () => {
      if (drawRef.current && map) {
        try {
          map.removeControl(drawRef.current);
          map.off('draw.create', handleDrawCreate);
          map.off('draw.update', handleDrawUpdate);
          map.off('draw.delete', handleDrawDelete);
          drawRef.current = null;
        } catch (error) {
          console.warn('Error removing draw control:', error);
        }
      }
      window.removeEventListener('maplibre:startDraw', handleStartDraw as EventListener);
    };
  }, [map]);

  // Custom circle drawing function
  const drawCircle = () => {
    if (!map || !drawRef.current) return;

    let center: [number, number] | null = null;
    let isDrawing = false;

    const handleClick = (e: maplibregl.MapMouseEvent) => {
      if (!isDrawing) {
        center = [e.lngLat.lng, e.lngLat.lat];
        isDrawing = true;
        map!.getCanvas().style.cursor = 'crosshair';
      } else {
        if (center) {
          const radius = calculateDistance(center, [e.lngLat.lng, e.lngLat.lat]);
          const circle = createCircleGeoJSON(center, radius);

          // Add circle to draw
          drawRef.current!.add(circle);

          // Reset
          center = null;
          isDrawing = false;
          map!.getCanvas().style.cursor = '';
          map!.off('click', handleClick);
          map!.off('mousemove', handleMouseMove);
        }
      }
    };

    const handleMouseMove = (e: maplibregl.MapMouseEvent) => {
      if (isDrawing && center) {
        // Show preview circle (optional)
        const radius = calculateDistance(center, [e.lngLat.lng, e.lngLat.lat]);
        // You could add a preview circle here
      }
    };

    map.on('click', handleClick);
    map.on('mousemove', handleMouseMove);
  };

  // Helper function to calculate distance between two points
  const calculateDistance = (point1: [number, number], point2: [number, number]): number => {
    const R = 6371000; // Earth's radius in meters
    const lat1 = point1[1] * Math.PI / 180;
    const lat2 = point2[1] * Math.PI / 180;
    const deltaLat = (point2[1] - point1[1]) * Math.PI / 180;
    const deltaLng = (point2[0] - point1[0]) * Math.PI / 180;

    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  };

  // Helper function to create circle GeoJSON
  const createCircleGeoJSON = (center: [number, number], radius: number) => {
    const points = 64;
    const coordinates = [];

    for (let i = 0; i < points; i++) {
      const angle = (i / points) * 2 * Math.PI;
      const dx = radius * Math.cos(angle);
      const dy = radius * Math.sin(angle);

      // Convert meters to degrees (approximate)
      const deltaLat = dy / 111320;
      const deltaLng = dx / (111320 * Math.cos(center[1] * Math.PI / 180));

      coordinates.push([
        center[0] + deltaLng,
        center[1] + deltaLat
      ]);
    }

    // Close the polygon
    coordinates.push(coordinates[0]);

    return {
      type: 'Feature',
      properties: {
        type: 'circle',
        center: center,
        radius: radius
      },
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates]
      }
    };
  };

  // Get all drawn features
  const getAllFeatures = () => {
    if (!drawRef.current) return [];
    return drawRef.current.getAll();
  };

  // Clear all drawn features
  const clearAll = () => {
    if (!drawRef.current) return;
    drawRef.current.deleteAll();
  };

  // Export functions for external use
  React.useImperativeHandle(drawRef, () => ({
    getAllFeatures,
    clearAll,
    getDrawInstance: () => drawRef.current
  }));

  return null;
};

export default MapLibreDrawing;
