import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import { useMap } from 'react-leaflet';
import { Incident, IncidentType } from '@/types/incident';
import { incidentTypeColors } from '@/services/map/mapService';
import { isValidCoordinates, getSafeCoordinates } from '@/utils/mapUtils';

interface MarkerClusterProps {
  incidents: Incident[];
  onSelectIncident: (id: string) => void;
}

const MarkerCluster: React.FC<MarkerClusterProps> = ({ incidents, onSelectIncident }) => {
  const map = useMap();
  const clusterGroupRef = useRef<any>(null);

  useEffect(() => {
    if (!map) return;

    // Clean up function to remove existing cluster group
    const cleanup = () => {
      if (clusterGroupRef.current) {
        try {
          map.removeLayer(clusterGroupRef.current);
          clusterGroupRef.current = null;
        } catch (error) {
          console.warn('Error removing cluster layer:', error);
        }
      }
    };

    // Remove existing cluster group if it exists
    cleanup();

    // Try to create and add the cluster group
    try {
      // Check if MarkerClusterGroup is available
      // @ts-ignore
      if (typeof L.markerClusterGroup !== 'function') {
        console.warn('MarkerClusterGroup not available');
        return cleanup;
      }

      // Create a new cluster group
      // @ts-ignore
      const clusterGroup = L.markerClusterGroup({
        showCoverageOnHover: true,
        zoomToBoundsOnClick: true,
        spiderfyOnMaxZoom: true,
        removeOutsideVisibleBounds: true,
        disableClusteringAtZoom: 18,
        maxClusterRadius: 80,
        iconCreateFunction: (cluster: any) => {
          try {
            // Get all markers in this cluster
            const markers = cluster.getAllChildMarkers();

            // Count incidents by type
            const typeCounts: Record<string, number> = {};
            markers.forEach((marker: any) => {
              const type = marker.options.incidentType;
              typeCounts[type] = (typeCounts[type] || 0) + 1;
            });

            // Find the dominant type (most common)
            let dominantType = '';
            let maxCount = 0;

            Object.entries(typeCounts).forEach(([type, count]) => {
              if (count > maxCount) {
                maxCount = count;
                dominantType = type;
              }
            });

            // Determine cluster size class
            const count = cluster.getChildCount();
            let size = 'small';

            if (count > 50) {
              size = 'large';
            } else if (count > 20) {
              size = 'medium';
            }

            // Create custom icon with dominant type color
            return L.divIcon({
              html: `<div><span>${count}</span></div>`,
              className: `marker-cluster marker-cluster-${size} marker-cluster-${dominantType.toLowerCase()}`,
              iconSize: L.point(40, 40)
            });
          } catch (error) {
            console.warn('Error creating cluster icon:', error);

            // Fallback icon
            return L.divIcon({
              html: `<div><span>${cluster.getChildCount()}</span></div>`,
              className: 'marker-cluster marker-cluster-small',
              iconSize: L.point(40, 40)
            });
          }
        }
      });

      // Add markers to the cluster group, filtering out invalid coordinates
      incidents
        .filter(incident => isValidCoordinates(incident.location))
        .forEach(incident => {
          try {
            // Get safe coordinates
            const safeLocation = getSafeCoordinates(incident.location);

            const marker = L.marker([safeLocation.latitude, safeLocation.longitude], {
              // @ts-ignore - Add custom property for incident type
              incidentType: incident.type,
              title: incident.title
            });

          // Add double-click event handler
          marker.on('dblclick', () => {
            onSelectIncident(incident.id);
          });

          // Create popup content
          const popupContent = document.createElement('div');
          popupContent.className = 'p-2';

          const title = document.createElement('h3');
          title.className = 'font-medium text-gray-900';
          title.textContent = incident.title;
          popupContent.appendChild(title);

          const badgeContainer = document.createElement('div');
          badgeContainer.className = 'flex flex-wrap gap-1 mt-2';

          const typeBadge = document.createElement('span');
          typeBadge.className = 'px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800';
          typeBadge.textContent = incident.type;
          badgeContainer.appendChild(typeBadge);

          const severityClass =
            incident.severity === 'CRITICAL' ? 'bg-red-100 text-red-800' :
            incident.severity === 'HIGH' ? 'bg-orange-100 text-orange-800' :
            incident.severity === 'MEDIUM' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800';

          const severityBadge = document.createElement('span');
          severityBadge.className = `px-2 py-1 text-xs font-medium rounded-full ${severityClass}`;
          severityBadge.textContent = incident.severity;
          badgeContainer.appendChild(severityBadge);

          popupContent.appendChild(badgeContainer);

          const address = document.createElement('p');
          address.className = 'text-sm text-gray-600 mt-2';
          address.textContent = incident.address;
          popupContent.appendChild(address);

          const button = document.createElement('button');
          button.className = 'mt-2 w-full px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded hover:bg-blue-700';
          button.textContent = 'View Details';
          button.onclick = () => onSelectIncident(incident.id);
          popupContent.appendChild(button);

          // Add popup to marker
          marker.bindPopup(popupContent);

          // Add marker to cluster group
          clusterGroup.addLayer(marker);
        } catch (error) {
          console.warn('Error adding marker to cluster:', error);
        }
      });

      // Add cluster group to map
      map.addLayer(clusterGroup);
      clusterGroupRef.current = clusterGroup;
    } catch (error) {
      console.warn('Error creating marker cluster:', error);
    }

    return cleanup;
  }, [map, incidents, onSelectIncident]);

  return null;
};

export default MarkerCluster;
