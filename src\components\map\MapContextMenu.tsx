import React from 'react';
import { LatLng } from 'leaflet';

interface MapContextMenuProps {
  position: { x: number; y: number };
  mapPosition: LatLng;
  onAddIncident: () => void;
  onAddResponse: () => void;
  onClose: () => void;
}

const MapContextMenu: React.FC<MapContextMenuProps> = ({
  position,
  mapPosition,
  onAddIncident,
  onAddResponse,
  onClose
}) => {
  // Close the menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.map-context-menu')) {
        onClose();
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [onClose]);

  return (
    <div 
      className="map-context-menu absolute z-50 bg-gray-800 border border-gray-700 rounded shadow-lg"
      style={{ 
        top: `${position.y}px`, 
        left: `${position.x}px`,
      }}
    >
      <ul className="py-1">
        <li 
          className="px-4 py-2 hover:bg-gray-700 cursor-pointer flex items-center text-white"
          onClick={onAddIncident}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Add Incident
        </li>
        <li 
          className="px-4 py-2 hover:bg-gray-700 cursor-pointer flex items-center text-white"
          onClick={onAddResponse}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          Add Response
        </li>
      </ul>
    </div>
  );
};

export default MapContextMenu;
