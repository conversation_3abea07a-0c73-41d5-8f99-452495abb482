import React, { useEffect } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';

interface MapViewPreserverProps {
  center: { lat: number; lng: number };
  zoom: number;
}

/**
 * Component to preserve map view (center and zoom) when parent components re-render
 * This prevents the map from resetting its view when filters change
 */
const MapViewPreserver: React.FC<MapViewPreserverProps> = ({ center, zoom }) => {
  const map = useMap();

  // Set the map view only when the component mounts
  useEffect(() => {
    // Get current view
    const currentCenter = map.getCenter();
    const currentZoom = map.getZoom();

    // Check if the view has changed significantly
    const centerChanged =
      Math.abs(currentCenter.lat - center.lat) > 0.0001 ||
      Math.abs(currentCenter.lng - center.lng) > 0.0001;
    const zoomChanged = currentZoom !== zoom;

    // Only set the view if it has changed significantly
    if (centerChanged || zoomChanged) {
      // Use flyTo for a smooth transition
      map.flyTo([center.lat, center.lng], zoom, {
        duration: 0.5, // Short duration to avoid long animations
        easeLinearity: 0.5
      });
    }
  }, [map, center, zoom]);

  return null;
};

export default MapViewPreserver;
