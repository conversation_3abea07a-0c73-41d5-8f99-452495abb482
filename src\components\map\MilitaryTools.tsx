import React, { useState, useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import {
  Target,
  Crosshair,
  Ruler,
  Radio,
  Zap,
  MapPin,
  Square,
  Circle,
  Type,
  Pentagon,
  Hexagon,
  Triangle,
  Pencil,
  Edit2,
  Trash
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { createRangeRing, createLineOfSight } from './TacticalSymbols';

interface MilitaryToolsProps {
  drawnItems: L.FeatureGroup | null;
}

const MilitaryTools: React.FC<MilitaryToolsProps> = ({ drawnItems }) => {
  const map = useMap();
  const [activeMode, setActiveMode] = useState<string | null>(null);
  const [rangeRingCenter, setRangeRingCenter] = useState<L.LatLng | null>(null);
  const [rangeRingRadius, setRangeRingRadius] = useState<number>(5); // Default 5km
  const [lineOfSightStart, setLineOfSightStart] = useState<L.LatLng | null>(null);

  const rangeRingRef = useRef<L.Circle | null>(null);
  const lineOfSightRef = useRef<L.Polyline | null>(null);
  const drawControlRef = useRef<any>(null);

  // Initialize Leaflet Draw control directly in this component
  useEffect(() => {
    if (!map || !drawnItems) return;

    try {
      // Check if Leaflet Draw plugin is available
      // @ts-ignore
      if (typeof L.Control.Draw !== 'function') {
        console.error('Leaflet Draw plugin not available');
        return;
      }

      // Create draw control options with military styling
      const drawOptions = {
        position: 'topright', // This won't be visible, but we need to set it
        draw: {
          polyline: {
            shapeOptions: {
              color: '#5E8E3E',
              weight: 3
            }
          },
          polygon: {
            allowIntersection: false,
            drawError: {
              color: '#FFC300',
              message: '<strong>Error:</strong> shape edges cannot cross!'
            },
            shapeOptions: {
              color: '#5E8E3E'
            }
          },
          circle: {
            shapeOptions: {
              color: '#436E43'
            }
          },
          rectangle: {
            shapeOptions: {
              color: '#436E43'
            }
          },
          marker: true
        },
        edit: {
          featureGroup: drawnItems,
          remove: true
        }
      };

      // Create a hidden draw control - we'll use our own UI
      // @ts-ignore
      const drawControl = new L.Control.Draw(drawOptions);

      // Add the control to the map but hide it with CSS
      map.addControl(drawControl);

      // Hide all Leaflet Draw controls
      const drawControlElements = document.querySelectorAll('.leaflet-draw');
      drawControlElements.forEach(el => {
        (el as HTMLElement).style.display = 'none';
      });

      // Store the draw control in the ref for later use
      drawControlRef.current = drawControl;

      // Handle draw events
      const handleDrawCreated = (e: any) => {
        try {
          const layer = e.layer;
          drawnItems.addLayer(layer);
          setActiveMode(null);
          map.getContainer().style.cursor = '';
        } catch (error) {
          console.error('Error handling draw created event:', error);
        }
      };

      map.on('draw:created', handleDrawCreated);

      // Handle draw events to reset UI state
      map.on('draw:drawstart', () => {
        map.getContainer().style.cursor = 'crosshair';
      });

      map.on('draw:drawstop', () => {
        map.getContainer().style.cursor = '';
        setActiveMode(null);
      });

      map.on('draw:editstart', () => {
        map.getContainer().style.cursor = 'crosshair';
      });

      map.on('draw:editstop', () => {
        map.getContainer().style.cursor = '';
        setActiveMode(null);
      });

      map.on('draw:deletestart', () => {
        map.getContainer().style.cursor = 'crosshair';
      });

      map.on('draw:deletestop', () => {
        map.getContainer().style.cursor = '';
        setActiveMode(null);
      });

      return () => {
        // Clean up event listeners
        map.off('draw:created', handleDrawCreated);
        map.off('draw:drawstart');
        map.off('draw:drawstop');
        map.off('draw:editstart');
        map.off('draw:editstop');
        map.off('draw:deletestart');
        map.off('draw:deletestop');

        // Remove the control
        try {
          map.removeControl(drawControl);
        } catch (error) {
          console.error('Error removing draw control:', error);
        }
      };
    } catch (error) {
      console.error('Error initializing draw control:', error);
    }
  }, [map, drawnItems]);

  // Clean up any temporary drawing elements when component unmounts
  useEffect(() => {
    return () => {
      if (rangeRingRef.current) {
        map.removeLayer(rangeRingRef.current);
      }
      if (lineOfSightRef.current) {
        map.removeLayer(lineOfSightRef.current);
      }
    };
  }, [map]);

  // Handle map clicks based on active mode
  useEffect(() => {
    if (!map) return;

    const handleMapClick = (e: L.LeafletMouseEvent) => {
      if (activeMode === 'rangeRing') {
        handleRangeRingClick(e);
      } else if (activeMode === 'lineOfSight') {
        handleLineOfSightClick(e);
      }
    };

    if (activeMode === 'rangeRing' || activeMode === 'lineOfSight') {
      map.on('click', handleMapClick);
      // Change cursor to crosshair when in active mode
      map.getContainer().style.cursor = 'crosshair';
    } else {
      map.getContainer().style.cursor = '';
    }

    return () => {
      map.off('click', handleMapClick);
      map.getContainer().style.cursor = '';
    };
  }, [map, activeMode, rangeRingCenter, rangeRingRadius, lineOfSightStart]);

  // Handle range ring creation
  const handleRangeRingClick = (e: L.LeafletMouseEvent) => {
    if (!rangeRingCenter) {
      // First click - set center
      setRangeRingCenter(e.latlng);

      // Create initial range ring
      if (rangeRingRef.current) {
        map.removeLayer(rangeRingRef.current);
      }

      const ring = createRangeRing(e.latlng, rangeRingRadius);
      ring.addTo(map);
      rangeRingRef.current = ring;

      // Add to drawn items if available
      if (drawnItems) {
        drawnItems.addLayer(ring);
      }
    } else {
      // Second click - reset for next range ring
      setRangeRingCenter(null);
      rangeRingRef.current = null;
      setActiveMode(null);
    }
  };

  // Handle line of sight creation
  const handleLineOfSightClick = (e: L.LeafletMouseEvent) => {
    if (!lineOfSightStart) {
      // First click - set start point
      setLineOfSightStart(e.latlng);
    } else {
      // Second click - create line of sight
      const line = createLineOfSight(lineOfSightStart, e.latlng);
      line.addTo(map);

      // Add to drawn items if available
      if (drawnItems) {
        drawnItems.addLayer(line);
      }

      // Reset for next line
      setLineOfSightStart(null);
      setActiveMode(null);
    }
  };

  // Toggle active mode
  const toggleMode = (mode: string) => {
    // Cancel any active drawing handlers first
    cancelDrawing();

    if (activeMode === mode) {
      setActiveMode(null);
      setRangeRingCenter(null);
      setLineOfSightStart(null);
    } else {
      setActiveMode(mode);
    }
  };

  // Adjust range ring radius
  const adjustRangeRingRadius = (newRadius: number) => {
    setRangeRingRadius(newRadius);

    if (rangeRingCenter && rangeRingRef.current) {
      map.removeLayer(rangeRingRef.current);

      const ring = createRangeRing(rangeRingCenter, newRadius);
      ring.addTo(map);
      rangeRingRef.current = ring;

      // Update in drawn items if available
      if (drawnItems) {
        drawnItems.eachLayer(layer => {
          if (layer === rangeRingRef.current) {
            drawnItems.removeLayer(layer);
          }
        });
        drawnItems.addLayer(ring);
      }
    }
  };

  // Cancel any active drawing
  const cancelDrawing = () => {
    try {
      // Cancel any active drawing handlers
      if (map) {
        // @ts-ignore - Access draw handlers
        const drawHandlers = document.querySelectorAll('.leaflet-draw-actions');
        if (drawHandlers.length > 0) {
          // Find and click the cancel button
          const cancelButtons = document.querySelectorAll('.leaflet-draw-actions a[title="Cancel drawing"]');
          if (cancelButtons.length > 0) {
            (cancelButtons[0] as HTMLElement).click();
          }
        }
      }
    } catch (error) {
      console.error('Error canceling drawing:', error);
    }
  };

  // Enable drawing mode with Leaflet.Draw
  const enableDrawingMode = (drawingType: string) => {
    try {
      // Cancel any active mode
      setActiveMode(null);
      setRangeRingCenter(null);
      setLineOfSightStart(null);

      if (!map) {
        console.error('Map not available');
        return;
      }

      // Access the draw control
      const drawControl = drawControlRef.current;
      if (!drawControl) {
        console.error('Draw control not available');
        return;
      }

      // First, cancel any active drawing
      cancelDrawing();

      // Find the hidden button for this drawing type and click it
      // This is more reliable than accessing internal properties
      const drawingTypeClass = `.leaflet-draw-draw-${drawingType.toLowerCase()}`;
      const drawButton = document.querySelector(drawingTypeClass);

      if (drawButton) {
        (drawButton as HTMLElement).click();
      } else {
        // Fallback to direct handler access if button not found
        try {
          // Access the draw toolbar
          const drawToolbar = drawControl._toolbars?.draw;
          if (!drawToolbar) {
            console.error('Draw toolbar not available');
            return;
          }

          // Convert drawing type to lowercase for consistency
          const type = drawingType.toLowerCase();

          // Check if the drawing mode exists
          if (!drawToolbar._modes[type]) {
            console.error(`Drawing mode ${type} not available`);
            return;
          }

          // Enable the drawing mode
          drawToolbar._modes[type].handler.enable();
        } catch (error) {
          console.error('Error accessing draw handler:', error);
        }
      }
    } catch (error) {
      console.error('Error enabling drawing mode:', error);
    }
  };

  // Enable edit mode for drawn items
  const enableEditMode = () => {
    try {
      // Cancel any active mode
      setActiveMode(null);
      setRangeRingCenter(null);
      setLineOfSightStart(null);

      if (!map) {
        console.error('Map not available');
        return;
      }

      if (!drawnItems || drawnItems.getLayers().length === 0) {
        alert('No items to edit');
        return;
      }

      // Cancel any active drawing
      cancelDrawing();

      // Find the hidden edit button and click it
      const editButton = document.querySelector('.leaflet-draw-edit-edit');

      if (editButton) {
        (editButton as HTMLElement).click();
      } else {
        // Fallback to direct handler access if button not found
        try {
          // Access the draw control
          const drawControl = drawControlRef.current;
          if (!drawControl) {
            console.error('Draw control not available');
            return;
          }

          // Access the edit toolbar
          const editToolbar = drawControl._toolbars?.edit;
          if (!editToolbar) {
            console.error('Edit toolbar not available');
            return;
          }

          // Enable edit mode
          if (editToolbar._modes.edit && editToolbar._modes.edit.handler) {
            editToolbar._modes.edit.handler.enable();
          } else {
            console.error('Edit handler not available');
          }
        } catch (error) {
          console.error('Error accessing edit handler:', error);
        }
      }
    } catch (error) {
      console.error('Error enabling edit mode:', error);
    }
  };

  // Enable delete mode for drawn items
  const enableDeleteMode = () => {
    try {
      // Cancel any active mode
      setActiveMode(null);
      setRangeRingCenter(null);
      setLineOfSightStart(null);

      if (!map) {
        console.error('Map not available');
        return;
      }

      if (!drawnItems || drawnItems.getLayers().length === 0) {
        alert('No items to delete');
        return;
      }

      // Cancel any active drawing
      cancelDrawing();

      // Find the hidden delete button and click it
      const deleteButton = document.querySelector('.leaflet-draw-edit-remove');

      if (deleteButton) {
        (deleteButton as HTMLElement).click();
      } else {
        // Fallback to direct handler access if button not found
        try {
          // Access the draw control
          const drawControl = drawControlRef.current;
          if (!drawControl) {
            console.error('Draw control not available');
            return;
          }

          // Access the edit toolbar
          const editToolbar = drawControl._toolbars?.edit;
          if (!editToolbar) {
            console.error('Edit toolbar not available');
            return;
          }

          // Enable delete mode
          if (editToolbar._modes.remove && editToolbar._modes.remove.handler) {
            editToolbar._modes.remove.handler.enable();
          } else {
            console.error('Delete handler not available');
          }
        } catch (error) {
          console.error('Error accessing delete handler:', error);
        }
      }
    } catch (error) {
      console.error('Error enabling delete mode:', error);
    }
  };

  // Clear all drawn items
  const clearAllDrawings = () => {
    if (drawnItems) {
      drawnItems.clearLayers();
    }
  };

  return (
    <div>
      <div className="grid grid-cols-3 gap-1">
        {/* Military-specific tactical tools with simplified icons */}
        <Button
          size="sm"
          variant="ghost"
          className={`military-button ${activeMode === 'rangeRing' ? 'active' : ''}`}
          title="Range Ring"
          onClick={() => toggleMode('rangeRing')}
        >
          <div className="w-4 h-4 flex items-center justify-center">○</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className={`military-button ${activeMode === 'lineOfSight' ? 'active' : ''}`}
          title="Line of Sight"
          onClick={() => toggleMode('lineOfSight')}
        >
          <div className="w-4 h-4 flex items-center justify-center">↗</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Draw Marker"
          onClick={() => enableDrawingMode('marker')}
        >
          <div className="w-4 h-4 flex items-center justify-center">📍</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Draw Line"
          onClick={() => enableDrawingMode('polyline')}
        >
          <div className="w-4 h-4 flex items-center justify-center">—</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Draw Polygon"
          onClick={() => enableDrawingMode('polygon')}
        >
          <div className="w-4 h-4 flex items-center justify-center">▲</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Draw Rectangle"
          onClick={() => enableDrawingMode('rectangle')}
        >
          <div className="w-4 h-4 flex items-center justify-center">□</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Draw Circle"
          onClick={() => enableDrawingMode('circle')}
        >
          <div className="w-4 h-4 flex items-center justify-center">⊙</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Edit Drawings"
          onClick={enableEditMode}
        >
          <div className="w-4 h-4 flex items-center justify-center">✓</div>
        </Button>

        <Button
          size="sm"
          variant="ghost"
          className="military-button"
          title="Delete Drawings"
          onClick={enableDeleteMode}
        >
          <div className="w-4 h-4 flex items-center justify-center">✗</div>
        </Button>
      </div>

      {activeMode === 'rangeRing' && (
        <div className="mt-2">
          <div className="text-xs mb-1">Range: {rangeRingRadius} km</div>
          <input
            type="range"
            min="1"
            max="50"
            value={rangeRingRadius}
            onChange={(e) => adjustRangeRingRadius(parseInt(e.target.value))}
            className="w-full"
          />
        </div>
      )}

      {activeMode === 'lineOfSight' && (
        <div className="mt-2 text-xs">
          {!lineOfSightStart ?
            "Click to set observation point" :
            "Click to set target point"
          }
        </div>
      )}
    </div>
  );
};

export default MilitaryTools;
