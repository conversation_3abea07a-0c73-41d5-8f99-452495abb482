import React, { useEffect, useState } from 'react';
import L from 'leaflet';
import { useMap } from 'react-leaflet';
import Button from '@/components/ui/Button';
import { Download, X, FileJson, FileSpreadsheet, FileImage } from 'lucide-react';
import { Incident } from '@/types/incident';
import { createGeoJsonFromIncidents } from '@/services/map/mapService';
import tokml from 'tokml';

interface ExportToolsProps {
  incidents: Incident[];
  drawnItems: L.FeatureGroup | null;
}

const ExportTools: React.FC<ExportToolsProps> = ({ incidents, drawnItems }) => {
  const map = useMap();
  const [showPanel, setShowPanel] = useState(false);
  
  // Add export control to map
  useEffect(() => {
    if (!map) return;
    
    // Create custom control for exports
    const ExportControl = L.Control.extend({
      options: {
        position: 'topleft'
      },
      onAdd: function() {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
        const button = L.DomUtil.create('a', 'leaflet-control-export', container);
        button.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>';
        button.href = '#';
        button.title = 'Export map data';
        
        L.DomEvent.on(button, 'click', function(e) {
          L.DomEvent.stopPropagation(e);
          L.DomEvent.preventDefault(e);
          setShowPanel(prev => !prev);
        });
        
        return container;
      }
    });
    
    const exportControl = new ExportControl();
    map.addControl(exportControl);
    
    return () => {
      map.removeControl(exportControl);
    };
  }, [map]);
  
  // Export incidents as GeoJSON
  const exportIncidentsAsGeoJSON = () => {
    const geojson = createGeoJsonFromIncidents(incidents);
    downloadFile(
      JSON.stringify(geojson, null, 2),
      'incidents.geojson',
      'application/json'
    );
  };
  
  // Export incidents as CSV
  const exportIncidentsAsCSV = () => {
    // Create CSV header
    const headers = [
      'id',
      'title',
      'description',
      'type',
      'severity',
      'status',
      'latitude',
      'longitude',
      'address',
      'reportedAt',
      'resolvedAt',
      'reportedBy',
      'assignedTo'
    ];
    
    // Create CSV rows
    const rows = incidents.map(incident => [
      incident.id,
      `"${incident.title.replace(/"/g, '""')}"`,
      `"${incident.description.replace(/"/g, '""')}"`,
      incident.type,
      incident.severity,
      incident.status,
      incident.location.latitude,
      incident.location.longitude,
      `"${incident.address.replace(/"/g, '""')}"`,
      incident.reportedAt,
      incident.resolvedAt || '',
      incident.reportedBy,
      incident.assignedTo || ''
    ]);
    
    // Combine header and rows
    const csv = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    downloadFile(csv, 'incidents.csv', 'text/csv');
  };
  
  // Export drawn items as GeoJSON
  const exportDrawnItemsAsGeoJSON = () => {
    if (!drawnItems || drawnItems.getLayers().length === 0) {
      alert('No drawn items to export');
      return;
    }
    
    const geojson = drawnItems.toGeoJSON();
    downloadFile(
      JSON.stringify(geojson, null, 2),
      'drawn-items.geojson',
      'application/json'
    );
  };
  
  // Export drawn items as KML
  const exportDrawnItemsAsKML = () => {
    if (!drawnItems || drawnItems.getLayers().length === 0) {
      alert('No drawn items to export');
      return;
    }
    
    const geojson = drawnItems.toGeoJSON();
    const kml = tokml(geojson);
    
    downloadFile(kml, 'drawn-items.kml', 'application/vnd.google-earth.kml+xml');
  };
  
  // Export map as image
  const exportMapAsImage = () => {
    try {
      // Use leaflet-image library if available
      // For now, just use a simple approach with HTML2Canvas
      alert('Map image export coming soon!');
    } catch (error) {
      console.error('Error exporting map as image:', error);
      alert('Error exporting map as image');
    }
  };
  
  // Helper function to download a file
  const downloadFile = (content: string, fileName: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  return (
    <>
      {showPanel && (
        <div className="layer-control-panel" style={{ top: '50px' }}>
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-sm font-medium">Export Data</h3>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={() => setShowPanel(false)}
              title="Close panel"
            >
              <X size={16} />
            </Button>
          </div>
          
          <div className="space-y-2">
            <div>
              <h4 className="text-xs font-medium mb-1">Incidents</h4>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={exportIncidentsAsGeoJSON}
                  title="Export incidents as GeoJSON"
                >
                  <FileJson size={14} className="mr-1" />
                  GeoJSON
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={exportIncidentsAsCSV}
                  title="Export incidents as CSV"
                >
                  <FileSpreadsheet size={14} className="mr-1" />
                  CSV
                </Button>
              </div>
            </div>
            
            <div>
              <h4 className="text-xs font-medium mb-1">Drawn Items</h4>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={exportDrawnItemsAsGeoJSON}
                  title="Export drawn items as GeoJSON"
                  disabled={!drawnItems || drawnItems.getLayers().length === 0}
                >
                  <FileJson size={14} className="mr-1" />
                  GeoJSON
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={exportDrawnItemsAsKML}
                  title="Export drawn items as KML"
                  disabled={!drawnItems || drawnItems.getLayers().length === 0}
                >
                  <FileJson size={14} className="mr-1" />
                  KML
                </Button>
              </div>
            </div>
            
            <div>
              <h4 className="text-xs font-medium mb-1">Map</h4>
              <Button
                size="sm"
                variant="outline"
                onClick={exportMapAsImage}
                title="Export map as image"
              >
                <FileImage size={14} className="mr-1" />
                Image
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ExportTools;
