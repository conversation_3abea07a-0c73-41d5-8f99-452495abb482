import React from 'react';
import { useMap } from 'react-leaflet';
import { Download, Save } from 'lucide-react';
import Button from '@/components/ui/Button';
import { Incident } from '@/types/incident';
import { createGeoJsonFromIncidents } from '@/services/map/mapService';

interface ExportMapControlsProps {
  incidents: Incident[];
  activeBaseLayer: string;
}

const ExportMapControls: React.FC<ExportMapControlsProps> = ({ 
  incidents, 
  activeBaseLayer 
}) => {
  const map = useMap();

  // Export incidents as GeoJSON
  const exportIncidentsAsGeoJSON = () => {
    try {
      const geoJson = createGeoJsonFromIncidents(incidents);
      const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(geoJson));
      const downloadAnchorNode = document.createElement('a');
      downloadAnchorNode.setAttribute("href", dataStr);
      downloadAnchorNode.setAttribute("download", "incidents.geojson");
      document.body.appendChild(downloadAnchorNode);
      downloadAnchorNode.click();
      downloadAnchorNode.remove();
    } catch (error) {
      console.error('Export failed', error);
      alert('Export failed: ' + error);
    }
  };

  // Save current map view
  const saveMapView = () => {
    try {
      if (!map) {
        throw new Error('Map not available');
      }
      
      const mapState = {
        center: map.getCenter(),
        zoom: map.getZoom(),
        baseLayer: activeBaseLayer
      };
      
      localStorage.setItem('mapState', JSON.stringify(mapState));
      alert('Map view saved successfully');
    } catch (error) {
      console.error('Save failed', error);
      alert('Save failed: ' + error);
    }
  };

  return (
    <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-[1000]">
      <div className="military-control-container rounded-md shadow-lg overflow-hidden">
        <div className="flex items-center justify-between px-2 py-1">
          <div className="flex items-center space-x-1">
            <Download size={16} />
            <span className="font-bold text-xs tracking-wider">EXPORT</span>
          </div>
          <div className="flex space-x-1">
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Export Incidents as GeoJSON"
              onClick={exportIncidentsAsGeoJSON}
            >
              <div className="w-4 h-4 flex items-center justify-center">⬇</div>
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Save Current View"
              onClick={saveMapView}
            >
              <div className="w-4 h-4 flex items-center justify-center">💾</div>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportMapControls;
