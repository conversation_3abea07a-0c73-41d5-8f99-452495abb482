import React, { useEffect, useState, useRef } from 'react';
import L from 'leaflet';
import { useMap } from 'react-leaflet';
import * as turf from '@turf/turf';
import { Incident } from '@/types/incident';
import Button from '@/components/ui/Button';
import { Filter, X, List } from 'lucide-react';

interface SpatialFilterProps {
  incidents: Incident[];
  drawnItems: L.FeatureGroup | null;
  onFilteredIncidents: (incidents: Incident[]) => void;
}

const SpatialFilter: React.FC<SpatialFilterProps> = ({ 
  incidents, 
  drawnItems, 
  onFilteredIncidents 
}) => {
  const map = useMap();
  const [isFiltering, setIsFiltering] = useState(false);
  const [filteredIncidents, setFilteredIncidents] = useState<Incident[]>([]);
  const [showPanel, setShowPanel] = useState(false);
  
  // Function to filter incidents by drawn shapes
  const filterIncidentsByShape = () => {
    if (!drawnItems) return;
    
    // Get all drawn layers
    const layers = drawnItems.getLayers();
    if (layers.length === 0) {
      alert('Please draw a shape on the map first');
      return;
    }
    
    setIsFiltering(true);
    
    // Convert drawn layers to GeoJSON
    const drawnShapes = drawnItems.toGeoJSON();
    
    // Filter incidents that are within any of the drawn shapes
    const filtered = incidents.filter(incident => {
      // Create a GeoJSON point for the incident
      const point = turf.point([incident.location.longitude, incident.location.latitude]);
      
      // Check if the point is within any of the drawn shapes
      return drawnShapes.features.some(feature => {
        try {
          if (feature.geometry.type === 'Point') {
            // For point features, check if they're close enough (within 100m)
            return turf.distance(point, feature as any) < 0.1; // 0.1 km = 100m
          } else {
            // For polygons, lines, etc.
            return turf.booleanPointInPolygon(point, feature as any);
          }
        } catch (error) {
          console.error('Error in spatial filtering:', error);
          return false;
        }
      });
    });
    
    setFilteredIncidents(filtered);
    onFilteredIncidents(filtered);
    setShowPanel(true);
  };
  
  // Reset spatial filter
  const resetFilter = () => {
    setIsFiltering(false);
    setFilteredIncidents([]);
    onFilteredIncidents(incidents);
    setShowPanel(false);
  };
  
  // Add filter control to map
  useEffect(() => {
    if (!map) return;
    
    // Create custom control for spatial filtering
    const SpatialFilterControl = L.Control.extend({
      options: {
        position: 'topleft'
      },
      onAdd: function() {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
        const button = L.DomUtil.create('a', 'leaflet-control-spatial-filter', container);
        button.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon></svg>';
        button.href = '#';
        button.title = 'Filter incidents by drawn shapes';
        
        L.DomEvent.on(button, 'click', function(e) {
          L.DomEvent.stopPropagation(e);
          L.DomEvent.preventDefault(e);
          filterIncidentsByShape();
        });
        
        return container;
      }
    });
    
    const filterControl = new SpatialFilterControl();
    map.addControl(filterControl);
    
    return () => {
      map.removeControl(filterControl);
    };
  }, [map, incidents, drawnItems]);
  
  return (
    <>
      {showPanel && (
        <div className="spatial-filter-panel">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium">Filtered Incidents</h3>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={resetFilter}
              title="Clear filter"
            >
              <X size={16} />
            </Button>
          </div>
          
          <div className="text-xs mb-2">
            Found {filteredIncidents.length} incidents within the drawn area
          </div>
          
          <div className="max-h-[200px] overflow-y-auto">
            {filteredIncidents.length > 0 ? (
              <ul className="space-y-1">
                {filteredIncidents.map(incident => (
                  <li key={incident.id} className="text-xs p-1 hover:bg-gray-100 rounded">
                    <div className="font-medium">{incident.title}</div>
                    <div className="text-gray-500">{incident.address}</div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-gray-500 text-xs">No incidents found in the selected area</div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default SpatialFilter;
