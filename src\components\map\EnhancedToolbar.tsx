import React, { useState, useEffect, useRef, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import Button from '@/components/ui/Button';
import {
  MapPin,
  Home,
  Trash2,
  Maximize2,
  Minimize2,
  Crosshair,
  Map,
  Grid,
  Mountain,
  Globe,
  Moon,
  Satellite,
  Layers,
  Target,
  Circle,
  Square,
  Pencil,
  Box,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  Settings,
  Eye,
  EyeOff,
  Pin,
  PinOff
} from 'lucide-react';

interface ToolbarGroup {
  id: string;
  title: string;
  icon: React.ReactNode;
  position: 'top' | 'right' | 'bottom' | 'left';
  children: React.ReactNode;
  collapsible: boolean;
  autoHide: boolean;
  pinned: boolean;
}

interface EnhancedToolbarProps {
  map: maplibregl.Map | null;
  activeBaseLayer: string;
  onBaseLayerChange: (layer: string) => void;
  mapMode: 'normal' | 'cluster' | 'heatmap';
  onMapModeChange: (mode: 'normal' | 'cluster' | 'heatmap') => void;
  is3D: boolean;
  onToggle3D: () => void;
}

const EnhancedToolbar: React.FC<EnhancedToolbarProps> = ({
  map,
  activeBaseLayer,
  onBaseLayerChange,
  mapMode,
  onMapModeChange,
  is3D,
  onToggle3D
}) => {
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [windowSize, setWindowSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  // Toolbar state management
  const [toolbarGroups, setToolbarGroups] = useState<ToolbarGroup[]>([]);
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(new Set());
  const [hiddenGroups, setHiddenGroups] = useState<Set<string>>(new Set());
  const [pinnedGroups, setPinnedGroups] = useState<Set<string>>(new Set(['navigation', 'baseLayer']));

  // Auto-hide functionality
  const [isMouseOverMap, setIsMouseOverMap] = useState(false);
  const [lastMouseActivity, setLastMouseActivity] = useState(Date.now());
  const autoHideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const AUTO_HIDE_DELAY = 3000; // 3 seconds

  // Track window size for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Mouse coordinate tracking
  useEffect(() => {
    if (!map) return;

    const handleMouseMove = (e: maplibregl.MapMouseEvent) => {
      setCoordinates({
        lat: parseFloat(e.lngLat.lat.toFixed(6)),
        lng: parseFloat(e.lngLat.lng.toFixed(6))
      });
    };

    map.on('mousemove', handleMouseMove);
    return () => map.off('mousemove', handleMouseMove);
  }, [map]);

  const handleHome = () => {
    if (!map) return;
    map.flyTo({
      center: [71.5249, 34.0151], // Pakistan coordinates
      zoom: is3D ? 8 : 6,
      pitch: is3D ? 60 : 0,
      bearing: 0,
      duration: 2000
    });
  };

  const handleCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        if (map) {
          map.flyTo({
            center: [position.coords.longitude, position.coords.latitude],
            zoom: 15,
            duration: 2000
          });
        }
      },
      (error) => {
        console.error('Error getting location:', error);
        alert('Unable to get your location.');
      }
    );
  };

  const clearDrawnItems = () => {
    // Dispatch custom event for clearing drawings
    window.dispatchEvent(new CustomEvent('map:clearDrawings'));
  };

  const handleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const toggleGroupCollapse = (groupId: string) => {
    setCollapsedGroups(prev => {
      const newCollapsed = new Set(prev);
      if (newCollapsed.has(groupId)) {
        newCollapsed.delete(groupId);
      } else {
        newCollapsed.add(groupId);
      }
      return newCollapsed;
    });
  };

  const toggleGroupPin = (groupId: string) => {
    setPinnedGroups(prev => {
      const newPinned = new Set(prev);
      if (newPinned.has(groupId)) {
        newPinned.delete(groupId);
      } else {
        newPinned.add(groupId);
      }
      return newPinned;
    });
  };

  const shouldUseCompactLayout = windowSize.width < 768 || windowSize.height < 600;

  // Initialize toolbar groups
  useEffect(() => {
    const groups: ToolbarGroup[] = [
      {
        id: 'navigation',
        title: 'Navigation',
        icon: <Crosshair size={14} />,
        position: 'left',
        collapsible: false,
        autoHide: false,
        pinned: true,
        children: (
          <div className="flex flex-col gap-1">
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Home View"
              onClick={handleHome}
            >
              <Home size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Current Location"
              onClick={handleCurrentLocation}
            >
              <MapPin size={14} />
            </Button>
          </div>
        )
      },
      {
        id: 'baseLayer',
        title: 'Base Layers',
        icon: <Layers size={14} />,
        position: 'right',
        collapsible: true,
        autoHide: true,
        pinned: false,
        children: (
          <div className="flex flex-col gap-1">
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'satellite' ? 'active' : ''}`}
              title="Satellite"
              onClick={() => onBaseLayerChange('satellite')}
            >
              <Satellite size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'terrain' ? 'active' : ''}`}
              title="Terrain"
              onClick={() => onBaseLayerChange('terrain')}
            >
              <Mountain size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'osm' ? 'active' : ''}`}
              title="OpenStreetMap"
              onClick={() => onBaseLayerChange('osm')}
            >
              <Globe size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${activeBaseLayer === 'dark' ? 'active' : ''}`}
              title="Dark"
              onClick={() => onBaseLayerChange('dark')}
            >
              <Moon size={14} />
            </Button>
          </div>
        )
      },
      {
        id: 'viewModes',
        title: 'View Modes',
        icon: <Target size={14} />,
        position: 'right',
        collapsible: true,
        autoHide: true,
        pinned: false,
        children: (
          <div className="flex flex-col gap-1">
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'normal' ? 'active' : ''}`}
              title="Normal View"
              onClick={() => onMapModeChange('normal')}
            >
              <MapPin size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'cluster' ? 'active' : ''}`}
              title="Cluster View"
              onClick={() => onMapModeChange('cluster')}
            >
              <Target size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${mapMode === 'heatmap' ? 'active' : ''}`}
              title="Heatmap View"
              onClick={() => onMapModeChange('heatmap')}
            >
              <Grid size={14} />
            </Button>
          </div>
        )
      },
      {
        id: 'mapControls',
        title: 'Map Controls',
        icon: <Settings size={14} />,
        position: 'bottom',
        collapsible: true,
        autoHide: true,
        pinned: false,
        children: (
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Clear All Drawings"
              onClick={clearDrawnItems}
            >
              <Trash2 size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className={`military-button ${is3D ? 'active' : ''}`}
              title={is3D ? 'Switch to 2D' : 'Switch to 3D'}
              onClick={onToggle3D}
            >
              <Box size={14} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="military-button"
              title="Fullscreen"
              onClick={handleFullscreen}
            >
              {isFullscreen ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
            </Button>
          </div>
        )
      }
    ];

    setToolbarGroups(groups);
  }, [activeBaseLayer, mapMode, is3D, isFullscreen]);

  const getPositionClasses = (position: string) => {
    switch (position) {
      case 'top':
        return 'top-2 left-1/2 transform -translate-x-1/2';
      case 'right':
        return 'top-2 right-2';
      case 'bottom':
        return 'bottom-2 left-1/2 transform -translate-x-1/2';
      case 'left':
        return 'top-2 left-2';
      default:
        return 'top-2 right-2';
    }
  };

  return (
    <>
      {/* Coordinates display */}
      {coordinates && (
        <div className="absolute bottom-2 left-2 z-[1000] military-control-container rounded-md shadow-lg overflow-hidden">
          <div className="px-2 py-1 text-xs font-mono">
            <div>Lat: {coordinates.lat}</div>
            <div>Lng: {coordinates.lng}</div>
          </div>
        </div>
      )}
    </>
  );
};

export default EnhancedToolbar;
