import React from 'react';
import { Bar, Pie, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartData
} from 'chart.js';
import { format } from 'date-fns';
import Card from '@/components/ui/Card';
import { useIncidentStore } from '@/store/incidentStore';
import { incidentTypeColors } from '@/services/map/mapService';
import { IncidentType, IncidentSeverity, IncidentStatus } from '@/types/incident';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler  // Add Filler plugin for fill option
);

// Chart options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        boxWidth: 12,
        usePointStyle: true,
        color: '#F5F5F5',
        font: {
          family: "'Roboto Mono', 'Courier New', monospace",
          size: 10
        }
      },
    },
    title: {
      color: '#F5F5F5',
      font: {
        family: "'Roboto Mono', 'Courier New', monospace",
        size: 12,
        weight: 'bold'
      }
    }
  },
  scales: {
    x: {
      ticks: {
        color: '#F5F5F5',
        font: {
          family: "'Roboto Mono', 'Courier New', monospace",
          size: 10
        }
      },
      grid: {
        color: 'rgba(74, 93, 35, 0.2)'
      }
    },
    y: {
      ticks: {
        color: '#F5F5F5',
        font: {
          family: "'Roboto Mono', 'Courier New', monospace",
          size: 10
        }
      },
      grid: {
        color: 'rgba(74, 93, 35, 0.2)'
      }
    }
  }
};

const StatisticsPanel: React.FC = () => {
  const { statistics } = useIncidentStore();

  if (!statistics) {
    return (
      <Card title="OPERATIONAL STATISTICS" variant="military" className="h-full">
        <div className="flex items-center justify-center h-64">
          <p className="text-military-white font-military-body">LOADING DATA...</p>
        </div>
      </Card>
    );
  }

  // Prepare data for incident types pie chart
  const typeData: ChartData<'pie'> = {
    labels: Object.keys(statistics.byType),
    datasets: [
      {
        data: Object.values(statistics.byType),
        backgroundColor: Object.keys(statistics.byType).map(
          (type) => incidentTypeColors[type as IncidentType]
        ),
        borderWidth: 1,
      },
    ],
  };

  // Prepare data for incident severity bar chart
  const severityData: ChartData<'bar'> = {
    labels: Object.keys(statistics.bySeverity),
    datasets: [
      {
        label: 'INCIDENTS BY SEVERITY',
        data: Object.values(statistics.bySeverity),
        backgroundColor: [
          'rgba(28, 37, 65, 0.8)', // LOW - navy blue
          'rgba(255, 191, 0, 0.8)', // MEDIUM - amber
          'rgba(74, 93, 35, 0.8)', // HIGH - olive green
          'rgba(193, 18, 31, 0.8)', // CRITICAL - signal red
        ],
        borderColor: [
          'rgba(28, 37, 65, 1)',
          'rgba(255, 191, 0, 1)',
          'rgba(74, 93, 35, 1)',
          'rgba(193, 18, 31, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Prepare data for incidents timeline
  const timelineData: ChartData<'line'> = {
    labels: statistics.byDay.slice(-30).map(item => item.date), // Last 30 days
    datasets: [
      {
        label: 'DAILY INCIDENTS',
        data: statistics.byDay.slice(-30).map(item => item.count),
        fill: true,
        backgroundColor: 'rgba(74, 93, 35, 0.2)',
        borderColor: 'rgba(94, 142, 62, 1)',
        tension: 0.4,
        pointBackgroundColor: 'rgba(94, 142, 62, 1)',
        pointBorderColor: '#F5F5F5',
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card title="INCIDENT TYPES" variant="military" className="h-full military-chart">
        <div className="h-64">
          <Pie data={typeData} options={chartOptions} />
        </div>
      </Card>

      <Card title="SEVERITY ANALYSIS" variant="military" className="h-full military-chart">
        <div className="h-64">
          <Bar data={severityData} options={chartOptions} />
        </div>
      </Card>

      <Card title="OPERATIONAL TIMELINE" variant="military" className="md:col-span-2 military-chart">
        <div className="h-64">
          <Line data={timelineData} options={chartOptions} />
        </div>
      </Card>

      <Card title="TACTICAL METRICS" variant="military" className="md:col-span-2">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="military-container bg-military-navy p-3">
            <h3 className="text-military-white text-xs font-military-body uppercase tracking-wider">TOTAL INCIDENTS</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.total}</p>
            <div className="h-1 w-full bg-military-border mt-2"></div>
          </div>

          <div className="military-container bg-military-darkgreen p-3">
            <h3 className="text-military-white text-xs font-military-body uppercase tracking-wider">RESOLVED</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.byStatus[IncidentStatus.RESOLVED] + statistics.byStatus[IncidentStatus.CLOSED]}</p>
            <div className="h-1 w-full bg-military-border mt-2"></div>
          </div>

          <div className="military-container bg-military-panel p-3">
            <h3 className="text-military-amber text-xs font-military-body uppercase tracking-wider">IN PROGRESS</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.byStatus[IncidentStatus.IN_PROGRESS]}</p>
            <div className="h-1 w-full bg-military-amber mt-2"></div>
          </div>

          <div className="military-container bg-military-panel p-3">
            <h3 className="text-military-blue text-xs font-military-body uppercase tracking-wider">CLOSED</h3>
            <p className="text-2xl font-military-heading text-military-white mt-2">{statistics.byStatus[IncidentStatus.CLOSED] || 0}</p>
            <div className="h-1 w-full bg-military-blue mt-2"></div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StatisticsPanel;