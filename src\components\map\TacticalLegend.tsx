import React, { useState, useCallback } from 'react';
import { ChevronUp, ChevronDown, Edit2, Save, X, Settings, Eye, EyeOff } from 'lucide-react';
import { IncidentType } from '@/types/incident';
import { tacticalSymbols } from './TacticalSymbols';
import Button from '@/components/ui/Button';

interface TacticalLegendProps {
  position?: 'bottomright' | 'bottomleft' | 'topright' | 'topleft';
}

const TacticalLegend: React.FC<TacticalLegendProps> = ({
  position = 'bottomright'
}) => {
  const [collapsed, setCollapsed] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [visibleCategories, setVisibleCategories] = useState({
    incidents: true,
    tactical: true
  });
  const [editingSymbol, setEditingSymbol] = useState<string | null>(null);
  const [editingDescription, setEditingDescription] = useState('');

  const positionClasses = {
    bottomright: 'bottom-2 right-2',
    bottomleft: 'bottom-2 left-20', // Offset to avoid toolbar overlap
    topright: 'top-2 right-2',
    topleft: 'top-2 left-2',
  };

  // Toggle category visibility
  const toggleCategory = useCallback((category: keyof typeof visibleCategories) => {
    setVisibleCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  }, []);

  // Start editing a symbol description
  const startEditing = useCallback((symbolKey: string, currentDescription: string) => {
    setEditingSymbol(symbolKey);
    setEditingDescription(currentDescription);
  }, []);

  // Save edited description
  const saveEdit = useCallback(() => {
    // In a real implementation, this would save to localStorage or a backend
    console.log(`Saving ${editingSymbol}: ${editingDescription}`);
    setEditingSymbol(null);
    setEditingDescription('');
  }, [editingSymbol, editingDescription]);

  // Cancel editing
  const cancelEdit = useCallback(() => {
    setEditingSymbol(null);
    setEditingDescription('');
  }, []);

  return (
    <div className={`absolute ${positionClasses[position]} z-[1000]`}>
      <div className="military-control-container rounded-md shadow-lg overflow-hidden max-w-[280px] min-w-[200px]">
        <div
          className="header flex items-center justify-between px-2 py-1 cursor-pointer hover:bg-gray-700"
          onClick={() => setCollapsed(!collapsed)}
        >
          <div className="flex items-center space-x-1">
            <span className="font-bold text-xs tracking-wider">LEGEND</span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant="ghost"
              className="military-button p-1"
              title={isEditing ? 'Exit Edit Mode' : 'Edit Legend'}
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(!isEditing);
              }}
            >
              {isEditing ? <X size={10} /> : <Edit2 size={10} />}
            </Button>
            {collapsed ? (
              <ChevronDown size={12} />
            ) : (
              <ChevronUp size={12} />
            )}
          </div>
        </div>

        {!collapsed && (
          <div className="content p-2 max-h-[400px] overflow-y-auto">
            {/* Original Incident Types */}
            <div className="mb-3">
              <div className="flex items-center justify-between text-xs font-bold text-military-accent mb-1 border-b border-military-accent/30 pb-1">
                <span>INCIDENT TYPES</span>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button p-1"
                  title={visibleCategories.incidents ? 'Hide Incidents' : 'Show Incidents'}
                  onClick={() => toggleCategory('incidents')}
                >
                  {visibleCategories.incidents ? <Eye size={10} /> : <EyeOff size={10} />}
                </Button>
              </div>
              {visibleCategories.incidents && (
                <div className="grid grid-cols-1 gap-1">
                  {[
                    IncidentType.PHYSICAL_RAID,
                    IncidentType.FIRE_RAID,
                    IncidentType.AMBUSH,
                    IncidentType.SNIPING,
                    IncidentType.POST_OVERRUN,
                    IncidentType.POST_FIRE,
                    IncidentType.DEMONSTRATION,
                    IncidentType.TARGET_KILLING,
                    IncidentType.ARSON
                  ].map(type => {
                    const symbol = tacticalSymbols[type];
                    if (!symbol) return null;

                    // Create SVG icon based on incident type
                    const svgIcon = `
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 32 32">
                        <g fill="${symbol.color}" stroke="#000" stroke-width="1">
                          ${getSvgPathForSymbol(symbol.symbol)}
                        </g>
                      </svg>
                    `;

                    // Convert SVG to data URL
                    const svgBase64 = btoa(svgIcon);
                    const dataUrl = `data:image/svg+xml;base64,${svgBase64}`;

                    const isEditing = editingSymbol === type;

                    return (
                      <div key={type} className="flex items-center space-x-2 py-0.5 group">
                        <img
                          src={dataUrl}
                          alt={type}
                          width={16}
                          height={16}
                          className="flex-shrink-0"
                        />
                        {isEditing ? (
                          <div className="flex items-center space-x-1 flex-1">
                            <input
                              type="text"
                              value={editingDescription}
                              onChange={(e) => setEditingDescription(e.target.value)}
                              className="text-xs bg-military-navy border border-military-border text-military-white px-1 py-0.5 flex-1"
                              autoFocus
                            />
                            <Button
                              size="sm"
                              variant="ghost"
                              className="military-button p-1"
                              onClick={saveEdit}
                            >
                              <Save size={8} />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="military-button p-1"
                              onClick={cancelEdit}
                            >
                              <X size={8} />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between flex-1">
                            <span className="text-xs text-military-white font-medium">
                              {symbol.description}
                            </span>
                            {isEditing && (
                              <Button
                                size="sm"
                                variant="ghost"
                                className="military-button p-1 opacity-0 group-hover:opacity-100"
                                onClick={() => startEditing(type, symbol.description)}
                              >
                                <Edit2 size={8} />
                              </Button>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Tactical Situation Types */}
            <div>
              <div className="flex items-center justify-between text-xs font-bold text-military-accent mb-1 border-b border-military-accent/30 pb-1">
                <span>TACTICAL SITUATIONS</span>
                <Button
                  size="sm"
                  variant="ghost"
                  className="military-button p-1"
                  title={visibleCategories.tactical ? 'Hide Tactical' : 'Show Tactical'}
                  onClick={() => toggleCategory('tactical')}
                >
                  {visibleCategories.tactical ? <Eye size={10} /> : <EyeOff size={10} />}
                </Button>
              </div>
              {visibleCategories.tactical && (
                <div className="grid grid-cols-1 gap-1">
                  {[
                    IncidentType.TS_ACTIVITY,
                    IncidentType.TS_INFIL,
                    IncidentType.TS_PRESENCE,
                    IncidentType.TS_MOV,
                    IncidentType.TS_TASKEEL,
                    IncidentType.TS_SB,
                    IncidentType.TS_EXTORTION,
                    IncidentType.TS_SUSPECT,
                    IncidentType.TS_MEETING,
                    IncidentType.TS_SEEN,
                    IncidentType.TS_TGT_KILLING,
                    IncidentType.TS_JIRGA,
                    IncidentType.OTHER
                  ].map(type => {
                    const symbol = tacticalSymbols[type];
                    if (!symbol) return null;

                    // Create SVG icon based on incident type
                    const svgIcon = `
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 32 32">
                        <g fill="${symbol.color}" stroke="#000" stroke-width="1">
                          ${getSvgPathForSymbol(symbol.symbol)}
                        </g>
                      </svg>
                    `;

                    // Convert SVG to data URL
                    const svgBase64 = btoa(svgIcon);
                    const dataUrl = `data:image/svg+xml;base64,${svgBase64}`;

                    const isEditingThis = editingSymbol === type;

                    return (
                      <div key={type} className="flex items-center space-x-2 py-0.5 group">
                        <img
                          src={dataUrl}
                          alt={type}
                          width={16}
                          height={16}
                          className="flex-shrink-0"
                        />
                        {isEditingThis ? (
                          <div className="flex items-center space-x-1 flex-1">
                            <input
                              type="text"
                              value={editingDescription}
                              onChange={(e) => setEditingDescription(e.target.value)}
                              className="text-xs bg-military-navy border border-military-border text-military-white px-1 py-0.5 flex-1"
                              autoFocus
                            />
                            <Button
                              size="sm"
                              variant="ghost"
                              className="military-button p-1"
                              onClick={saveEdit}
                            >
                              <Save size={8} />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="military-button p-1"
                              onClick={cancelEdit}
                            >
                              <X size={8} />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between flex-1">
                            <span className="text-xs text-military-white font-medium">
                              {symbol.description}
                            </span>
                            {isEditing && (
                              <Button
                                size="sm"
                                variant="ghost"
                                className="military-button p-1 opacity-0 group-hover:opacity-100"
                                onClick={() => startEditing(type, symbol.description)}
                              >
                                <Edit2 size={8} />
                              </Button>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Get SVG path for a specific tactical symbol (synchronized with TacticalSymbols.tsx)
const getSvgPathForSymbol = (symbolType: string): string => {
  switch (symbolType) {
    // Original incident symbols - Enhanced NATO-style tactical symbols
    case 'physical_raid':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,12 L24,12 M8,20 L24,20" stroke="#fff" stroke-width="2.5" /><circle cx="16" cy="16" r="3" fill="#fff" />';

    case 'fire_raid':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,6 Q12,10 14,14 Q10,18 16,22 Q22,18 18,14 Q20,10 16,6" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

    case 'ambush':
      return '<polygon points="16,2 2,16 16,30 30,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,16 L24,16 M16,8 L16,24" stroke="#fff" stroke-width="2.5" /><circle cx="12" cy="12" r="2" fill="#fff" /><circle cx="20" cy="20" r="2" fill="#fff" />';

    case 'sniping':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16 M16,6 L16,26" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="6" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="2" fill="#fff" />';

    case 'post_overrun':
      return '<rect x="3" y="3" width="26" height="26" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M7,7 L25,25 M7,25 L25,7" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="3" fill="#fff" />';

    case 'post_fire':
      return '<rect x="3" y="3" width="26" height="26" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,7 Q12,11 14,15 Q10,19 16,23 Q22,19 18,15 Q20,11 16,7" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

    case 'demonstration':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="11" r="2.5" fill="#fff" /><path d="M8,20 Q16,25 24,20" stroke="#fff" stroke-width="2" fill="none" />';

    case 'target_killing':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,8 L24,24 M8,24 L24,8" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="5" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="1.5" fill="#fff" />';

    case 'arson':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M16,6 Q12,10 14,14 Q10,18 16,22 Q22,18 18,14 Q20,10 16,6" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.3" />';

    // TS (Tactical Situation) symbols - Enhanced with better visibility
    case 'ts_activity':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="3" fill="#fff" /><path d="M16,8 L16,24 M8,16 L24,16" stroke="#fff" stroke-width="1.5" />';

    case 'ts_infil':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16" stroke="#fff" stroke-width="3" /><path d="M20,10 L26,16 L20,22" stroke="#fff" stroke-width="2.5" fill="none" /><circle cx="8" cy="16" r="2" fill="#fff" />';

    case 'ts_presence':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="#fff" fill-opacity="0.4" /><circle cx="16" cy="16" r="3" fill="#fff" />';

    case 'ts_mov':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 L26,16" stroke="#fff" stroke-width="3" /><path d="M18,10 L26,16 L18,22" stroke="#fff" stroke-width="2.5" fill="none" /><path d="M10,10 L18,16 L10,22" stroke="#fff" stroke-width="2" fill="none" />';

    case 'ts_taskeel':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="21" r="2.5" fill="#fff" /><path d="M10,16 L22,16" stroke="#fff" stroke-width="1.5" />';

    case 'ts_sb':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><rect x="8" y="10" width="16" height="12" fill="#fff" fill-opacity="0.3" stroke="#fff" stroke-width="1" /><text x="16" y="18" font-size="8" text-anchor="middle" fill="#fff" font-weight="bold">SB</text>';

    case 'ts_extortion':
      return '<polygon points="16,2 2,12 2,22 16,30 30,22 30,12" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="16" r="8" stroke="#fff" stroke-width="2" fill="none" /><text x="16" y="18" font-size="7" text-anchor="middle" fill="#fff" font-weight="bold">EXT</text>';

    case 'ts_suspect':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="16" cy="12" r="4" fill="#fff" /><path d="M12,20 Q16,24 20,20" stroke="#fff" stroke-width="2" fill="none" /><text x="16" y="26" font-size="6" text-anchor="middle" fill="#fff" font-weight="bold">SUSP</text>';

    case 'ts_meeting':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="11" r="2.5" fill="#fff" /><circle cx="22" cy="11" r="2.5" fill="#fff" /><circle cx="16" cy="11" r="2.5" fill="#fff" /><ellipse cx="16" cy="20" rx="8" ry="3" fill="#fff" fill-opacity="0.5" stroke="#fff" stroke-width="1" />';

    case 'ts_seen':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M6,16 Q16,6 26,16 Q16,26 6,16 Z" fill="#fff" fill-opacity="0.5" stroke="#fff" stroke-width="1.5" /><circle cx="16" cy="16" r="4" fill="#000" /><circle cx="16" cy="16" r="2" fill="#fff" />';

    case 'ts_tgt_killing':
      return '<polygon points="16,2 30,16 16,30 2,16" fill-opacity="0.9" stroke="#000" stroke-width="1" /><path d="M8,8 L24,24 M8,24 L24,8" stroke="#fff" stroke-width="3" /><circle cx="16" cy="16" r="6" stroke="#fff" stroke-width="2" fill="none" /><circle cx="16" cy="16" r="2" fill="#fff" />';

    case 'ts_jirga':
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><circle cx="10" cy="10" r="2" fill="#fff" /><circle cx="16" cy="10" r="2" fill="#fff" /><circle cx="22" cy="10" r="2" fill="#fff" /><ellipse cx="16" cy="20" rx="10" ry="4" fill="#fff" fill-opacity="0.3" stroke="#fff" stroke-width="1.5" /><path d="M8,22 Q16,18 24,22" stroke="#fff" stroke-width="1.5" fill="none" />';

    case 'other':
    default:
      return '<circle cx="16" cy="16" r="14" fill-opacity="0.9" stroke="#000" stroke-width="1" /><text x="16" y="20" font-size="12" text-anchor="middle" fill="#fff" font-weight="bold">?</text>';
  }
};

export default TacticalLegend;
