import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import { useMap } from 'react-leaflet';
import 'leaflet-routing-machine';
import { Incident } from '@/types/incident';
import Button from '@/components/ui/Button';
import { Navigation, X, CornerDownRight } from 'lucide-react';

interface RoutingMachineProps {
  incidents: Incident[];
}

const RoutingMachine: React.FC<RoutingMachineProps> = ({ incidents }) => {
  const map = useMap();
  const routingControlRef = useRef<L.Routing.Control | null>(null);
  const [showPanel, setShowPanel] = useState(false);
  const [selectedIncidents, setSelectedIncidents] = useState<Incident[]>([]);
  const [routeSummary, setRouteSummary] = useState<{
    distance: number;
    time: number;
  } | null>(null);
  
  // Add routing control to map
  useEffect(() => {
    if (!map) return;
    
    // Create custom control for routing
    const RoutingControl = L.Control.extend({
      options: {
        position: 'topleft'
      },
      onAdd: function() {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
        const button = L.DomUtil.create('a', 'leaflet-control-routing', container);
        button.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="3 11 22 2 13 21 11 13 3 11"></polygon></svg>';
        button.href = '#';
        button.title = 'Create route between incidents';
        
        L.DomEvent.on(button, 'click', function(e) {
          L.DomEvent.stopPropagation(e);
          L.DomEvent.preventDefault(e);
          setShowPanel(prev => !prev);
        });
        
        return container;
      }
    });
    
    const routingButton = new RoutingControl();
    map.addControl(routingButton);
    
    return () => {
      map.removeControl(routingButton);
      if (routingControlRef.current) {
        map.removeControl(routingControlRef.current);
      }
    };
  }, [map]);
  
  // Toggle incident selection for routing
  const toggleIncidentSelection = (incident: Incident) => {
    setSelectedIncidents(prev => {
      const isSelected = prev.some(i => i.id === incident.id);
      
      if (isSelected) {
        return prev.filter(i => i.id !== incident.id);
      } else {
        return [...prev, incident];
      }
    });
  };
  
  // Create route between selected incidents
  const createRoute = () => {
    if (selectedIncidents.length < 2) {
      alert('Please select at least 2 incidents to create a route');
      return;
    }
    
    // Remove existing routing control
    if (routingControlRef.current) {
      map.removeControl(routingControlRef.current);
    }
    
    // Create waypoints from selected incidents
    const waypoints = selectedIncidents.map(incident => 
      L.latLng(incident.location.latitude, incident.location.longitude)
    );
    
    // Create routing control
    // @ts-ignore
    const routingControl = L.Routing.control({
      waypoints,
      routeWhileDragging: true,
      showAlternatives: true,
      altLineOptions: {
        styles: [
          { color: 'black', opacity: 0.15, weight: 9 },
          { color: 'white', opacity: 0.8, weight: 6 },
          { color: 'blue', opacity: 0.5, weight: 2 }
        ]
      },
      lineOptions: {
        styles: [
          { color: 'black', opacity: 0.15, weight: 9 },
          { color: 'white', opacity: 0.8, weight: 6 },
          { color: 'blue', opacity: 0.5, weight: 2 }
        ]
      },
      router: L.Routing.osrmv1({
        serviceUrl: 'https://router.project-osrm.org/route/v1',
        profile: 'driving'
      }),
      createMarker: (i: number, waypoint: L.Routing.Waypoint) => {
        const incident = selectedIncidents[i];
        if (!incident) return L.marker(waypoint.latLng);
        
        const marker = L.marker(waypoint.latLng, {
          draggable: true,
          title: incident.title
        });
        
        marker.bindPopup(`<b>${incident.title}</b><br>${incident.address}`);
        
        return marker;
      }
    }).addTo(map);
    
    // Listen for route calculation
    routingControl.on('routesfound', (e: any) => {
      const routes = e.routes;
      const summary = routes[0].summary;
      
      setRouteSummary({
        distance: summary.totalDistance / 1000, // Convert to km
        time: summary.totalTime / 60 // Convert to minutes
      });
    });
    
    routingControlRef.current = routingControl;
  };
  
  // Clear route
  const clearRoute = () => {
    if (routingControlRef.current) {
      map.removeControl(routingControlRef.current);
      routingControlRef.current = null;
    }
    
    setSelectedIncidents([]);
    setRouteSummary(null);
  };
  
  // Format time for display
  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    
    if (hours === 0) {
      return `${mins} min`;
    } else {
      return `${hours} h ${mins} min`;
    }
  };
  
  return (
    <>
      {showPanel && (
        <div className="spatial-filter-panel" style={{ right: '10px', left: 'auto' }}>
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium">Create Route</h3>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={() => setShowPanel(false)}
              title="Close panel"
            >
              <X size={16} />
            </Button>
          </div>
          
          <div className="text-xs mb-2">
            Select incidents to create a route between them
          </div>
          
          <div className="max-h-[150px] overflow-y-auto mb-3">
            {incidents.length > 0 ? (
              <ul className="space-y-1">
                {incidents.map(incident => {
                  const isSelected = selectedIncidents.some(i => i.id === incident.id);
                  return (
                    <li 
                      key={incident.id} 
                      className={`text-xs p-1 rounded cursor-pointer flex items-start ${isSelected ? 'bg-blue-50' : 'hover:bg-gray-100'}`}
                      onClick={() => toggleIncidentSelection(incident)}
                    >
                      <div className="mr-1 mt-0.5">
                        {isSelected && <CornerDownRight size={12} />}
                      </div>
                      <div>
                        <div className="font-medium">{incident.title}</div>
                        <div className="text-gray-500">{incident.address}</div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            ) : (
              <div className="text-gray-500 text-xs">No incidents available</div>
            )}
          </div>
          
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="primary"
              className="flex-1"
              onClick={createRoute}
              disabled={selectedIncidents.length < 2}
            >
              <Navigation size={14} className="mr-1" />
              Create Route
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={clearRoute}
              disabled={!routingControlRef.current}
            >
              Clear
            </Button>
          </div>
          
          {routeSummary && (
            <div className="mt-3 text-xs p-2 bg-gray-50 rounded">
              <div className="font-medium">Route Summary:</div>
              <div>Distance: {routeSummary.distance.toFixed(1)} km</div>
              <div>Time: {formatTime(routeSummary.time)}</div>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default RoutingMachine;
