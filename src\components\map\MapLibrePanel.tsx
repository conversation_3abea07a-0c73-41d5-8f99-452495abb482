import React, { useRef, useEffect, useState, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Incident, Response } from '@/types/incident';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import {
  Map as MapIcon,
  Layers,
  Mountain,
  Globe,
  Moon,
  Satellite,
  Box,
  RotateCcw
} from 'lucide-react';

// Import MapLibre components
import MapLibreToolbar from './MapLibreToolbar';
import MapLibreMarkers from './MapLibreMarkers';
import MapLibreDrawing from './MapLibreDrawing';
import MapLibreContextMenu from './MapLibreContextMenu';
import TacticalLegend from './TacticalLegend';
import ViewshedAnalysis from './ViewshedAnalysis';
import EnhancedSymbology from './EnhancedSymbology';

interface MapLibrePanelProps {
  incidents: Incident[];
  onSelectIncident: (id: string) => void;
}

const MapLibrePanel: React.FC<MapLibrePanelProps> = ({ incidents, onSelectIncident }) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<maplibregl.Map | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [activeBaseLayer, setActiveBaseLayer] = useState('satellite');
  const [mapMode, setMapMode] = useState<'normal' | 'cluster' | 'heatmap'>('normal');
  const [is3D, setIs3D] = useState(false);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    lngLat: [number, number];
  } | null>(null);

  const { responses } = useResponseStore();

  // Function to apply military styling to navigation controls
  const applyMilitaryNavigationStyling = () => {
    // Apply military theme to navigation controls
    const style = document.createElement('style');
    style.textContent = `
      .maplibregl-ctrl-group {
        background: rgba(30, 41, 59, 0.95) !important;
        border: 2px solid #4a5568 !important;
        border-radius: 4px !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
        backdrop-filter: blur(8px) !important;
      }

      .maplibregl-ctrl-group button {
        background: transparent !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
        font-weight: bold !important;
        transition: all 0.2s ease !important;
        font-family: 'Courier New', monospace !important;
      }

      .maplibregl-ctrl-group button:hover {
        background: rgba(59, 130, 246, 0.3) !important;
        border-color: #3b82f6 !important;
        color: #ffffff !important;
        transform: scale(1.05) !important;
      }

      .maplibregl-ctrl-group button:active {
        background: rgba(59, 130, 246, 0.5) !important;
        transform: scale(0.95) !important;
      }

      .maplibregl-ctrl-compass .maplibregl-ctrl-compass-arrow {
        border-color: #e2e8f0 transparent transparent !important;
      }

      .maplibregl-ctrl-zoom-in,
      .maplibregl-ctrl-zoom-out {
        font-size: 16px !important;
        font-weight: bold !important;
      }

      .maplibregl-ctrl-zoom-in::before {
        content: "+" !important;
      }

      .maplibregl-ctrl-zoom-out::before {
        content: "−" !important;
      }
    `;
    document.head.appendChild(style);
  };

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    // Prevent multiple initialization
    if (mapContainer.current.hasChildNodes()) return;

    map.current = new maplibregl.Map({
      container: mapContainer.current,
      style: getMapStyle(activeBaseLayer),
      center: [71.5249, 34.0151], // Pakistan coordinates
      zoom: 6,
      pitch: 0,
      bearing: 0,
      antialias: true
    });

    // Add navigation controls to left side with military styling
    const navControl = new maplibregl.NavigationControl({
      showCompass: true,
      showZoom: true,
      visualizePitch: true
    });
    map.current.addControl(navControl, 'top-left');
    map.current.addControl(new maplibregl.ScaleControl(), 'bottom-right');

    // Map load event
    map.current.on('load', () => {
      setMapLoaded(true);

      // Add terrain source if not already present
      if (map.current && !map.current.getSource('terrarium')) {
        try {
          map.current.addSource('terrarium', {
            type: 'raster-dem',
            tiles: ['https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png'],
            tileSize: 256,
            encoding: 'terrarium'
          });
        } catch (error) {
          console.warn('Could not add terrain source:', error);
        }
      }

      // Apply military styling to navigation controls
      applyMilitaryNavigationStyling();
    });

    // Context menu
    map.current.on('contextmenu', (e) => {
      e.preventDefault();
      setContextMenu({
        x: e.point.x,
        y: e.point.y,
        lngLat: [e.lngLat.lng, e.lngLat.lat]
      });
    });

    // Close context menu on click
    map.current.on('click', () => {
      setContextMenu(null);
    });

    return () => {
      if (map.current) {
        try {
          map.current.remove();
        } catch (error) {
          console.warn('Error removing map:', error);
        } finally {
          map.current = null;
          setMapLoaded(false);
        }
      }
    };
  }, []);

  // Update map style when base layer changes
  useEffect(() => {
    if (map.current && mapLoaded) {
      map.current.setStyle(getMapStyle(activeBaseLayer));
    }
  }, [activeBaseLayer, mapLoaded]);

  // Toggle 3D view
  const toggle3D = useCallback(() => {
    if (!map.current) return;

    const newIs3D = !is3D;
    setIs3D(newIs3D);

    if (newIs3D) {
      // Enable 3D terrain and adjust camera
      map.current.easeTo({
        pitch: 60,
        bearing: 0,
        zoom: Math.max(map.current.getZoom(), 8), // Ensure good zoom level for terrain
        duration: 1000
      });

      // Add terrain if not already present
      if (!map.current.getTerrain()) {
        try {
          map.current.setTerrain({
            source: 'terrarium',
            exaggeration: 1.5
          });
        } catch (error) {
          console.warn('Could not enable terrain:', error);
        }
      }
    } else {
      // Disable 3D terrain and flatten view
      map.current.easeTo({
        pitch: 0,
        bearing: 0,
        duration: 1000
      });

      // Remove terrain
      try {
        map.current.setTerrain(null);
      } catch (error) {
        console.warn('Could not disable terrain:', error);
      }
    }
  }, [is3D]);

  // Get map style based on active layer
  const getMapStyle = (layer: string) => {
    const baseStyle = {
      version: 8,
      sources: {
        // Add terrain source for 3D elevation
        'terrarium': {
          type: 'raster-dem',
          tiles: ['https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png'],
          tileSize: 256,
          encoding: 'terrarium'
        }
      },
      layers: [],
      terrain: {
        source: 'terrarium',
        exaggeration: 1.5
      }
    };

    switch (layer) {
      case 'satellite':
        return {
          ...baseStyle,
          sources: {
            ...baseStyle.sources,
            'satellite': {
              type: 'raster',
              tiles: [
                'https://mt0.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}',
                'https://mt1.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}',
                'https://mt2.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}',
                'https://mt3.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}'
              ],
              tileSize: 256
            }
          },
          layers: [
            {
              id: 'satellite',
              type: 'raster',
              source: 'satellite'
            }
          ]
        };
      case 'terrain':
        return {
          ...baseStyle,
          sources: {
            ...baseStyle.sources,
            'terrain-base': {
              type: 'raster',
              tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
              tileSize: 256
            },
            'terrain-hillshade': {
              type: 'raster',
              tiles: [
                'https://mt0.google.com/vt/lyrs=p&x={x}&y={y}&z={z}',
                'https://mt1.google.com/vt/lyrs=p&x={x}&y={y}&z={z}',
                'https://mt2.google.com/vt/lyrs=p&x={x}&y={y}&z={z}',
                'https://mt3.google.com/vt/lyrs=p&x={x}&y={y}&z={z}'
              ],
              tileSize: 256
            }
          },
          layers: [
            {
              id: 'terrain-base',
              type: 'raster',
              source: 'terrain-base'
            },
            {
              id: 'terrain-hillshade',
              type: 'raster',
              source: 'terrain-hillshade',
              paint: {
                'raster-opacity': 0.7
              }
            }
          ]
        };
      case 'osm':
        return {
          ...baseStyle,
          sources: {
            ...baseStyle.sources,
            'osm': {
              type: 'raster',
              tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
              tileSize: 256,
              attribution: '© OpenStreetMap contributors'
            }
          },
          layers: [
            {
              id: 'osm',
              type: 'raster',
              source: 'osm'
            }
          ]
        };
      case 'dark':
        return {
          ...baseStyle,
          sources: {
            ...baseStyle.sources,
            'dark': {
              type: 'raster',
              tiles: ['https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png'],
              tileSize: 256
            }
          },
          layers: [
            {
              id: 'dark',
              type: 'raster',
              source: 'dark'
            }
          ]
        };
      default:
        return {
          ...baseStyle,
          sources: {
            ...baseStyle.sources,
            'osm': {
              type: 'raster',
              tiles: ['https://tile.openstreetmap.org/{z}/{x}/{y}.png'],
              tileSize: 256,
              attribution: '© OpenStreetMap contributors'
            }
          },
          layers: [
            {
              id: 'osm',
              type: 'raster',
              source: 'osm'
            }
          ]
        };
    }
  };

  const handleAddIncident = () => {
    if (contextMenu) {
      // Trigger incident form with coordinates
      const event = new CustomEvent('map:addIncident', {
        detail: {
          coordinates: {
            latitude: contextMenu.lngLat[1],
            longitude: contextMenu.lngLat[0]
          }
        }
      });
      window.dispatchEvent(event);
      setContextMenu(null);
    }
  };

  const handleAddResponse = () => {
    if (contextMenu) {
      // Trigger response form with coordinates
      const event = new CustomEvent('map:addResponse', {
        detail: {
          coordinates: {
            latitude: contextMenu.lngLat[1],
            longitude: contextMenu.lngLat[0]
          }
        }
      });
      window.dispatchEvent(event);
      setContextMenu(null);
    }
  };

  return (
    <Card
      title="TACTICAL MAP"
      variant="military"
      className="h-full flex flex-col overflow-hidden"
      headerAction={
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="military"
            leftIcon={<Box size={14} />}
            onClick={toggle3D}
            className={`military-btn ${is3D ? 'bg-military-blue' : 'bg-military-darkgray'} hover:bg-military-navy`}
            title={is3D ? 'Switch to 2D' : 'Switch to 3D'}
          >
            {is3D ? '2D' : '3D'}
          </Button>
        </div>
      }
    >
      <div className="h-full rounded-md overflow-hidden relative">
        {/* Map container */}
        <div ref={mapContainer} className="w-full h-full" />

        {/* Context menu */}
        {contextMenu && (
          <MapLibreContextMenu
            position={{ x: contextMenu.x, y: contextMenu.y }}
            onAddIncident={handleAddIncident}
            onAddResponse={handleAddResponse}
            onClose={() => setContextMenu(null)}
          />
        )}

        {/* Map toolbar */}
        {mapLoaded && (
          <MapLibreToolbar
            map={map.current}
            activeBaseLayer={activeBaseLayer}
            onBaseLayerChange={setActiveBaseLayer}
            mapMode={mapMode}
            onMapModeChange={setMapMode}
            is3D={is3D}
            onToggle3D={toggle3D}
          />
        )}

        {/* Markers and data layers */}
        {mapLoaded && (
          <MapLibreMarkers
            map={map.current}
            incidents={incidents}
            responses={responses}
            mode={mapMode}
            onSelectIncident={onSelectIncident}
          />
        )}

        {/* Drawing tools */}
        {mapLoaded && (
          <MapLibreDrawing
            map={map.current}
          />
        )}

        {/* Tactical Legend positioned to avoid overlap with scale control */}
        {mapLoaded && (
          <TacticalLegend position="bottomleft" />
        )}

        {/* Viewshed Analysis */}
        {mapLoaded && (
          <ViewshedAnalysis map={map.current} />
        )}

        {/* Enhanced Symbology */}
        {mapLoaded && (
          <EnhancedSymbology
            onSymbolUpdate={(type, config) => {
              console.log('Symbol updated:', type, config);
              // In a real implementation, this would update the marker rendering
            }}
          />
        )}
      </div>
    </Card>
  );
};

// Main component wrapper
const MapLibrePanelWrapper: React.FC = () => {
  const { filteredIncidents, selectIncident } = useIncidentStore();

  const handleSelectIncident = (id: string) => {
    selectIncident(id);
  };

  return (
    <MapLibrePanel
      incidents={filteredIncidents}
      onSelectIncident={handleSelectIncident}
    />
  );
};

export default MapLibrePanelWrapper;
