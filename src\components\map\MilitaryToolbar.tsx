import React, { useState, useEffect } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import {
  MapPin,
  Home,
  Trash2,
  Maximize2,
  Minimize2,
  Crosshair,
  Map,
  Grid,
  Mountain,
  Globe,
  Moon,
  MapPinned
} from 'lucide-react';
import Button from '@/components/ui/Button';

interface MilitaryToolbarProps {
  drawnItems: L.FeatureGroup | null;
  onDrawCreated?: (layer: L.Layer) => void;
  onFullscreenToggle?: () => void;
  onBaseLayerChange?: (layerName: string) => void;
  activeBaseLayer: string;
}

const MilitaryToolbar: React.FC<MilitaryToolbarProps> = ({
  drawnItems,
  onDrawCreated,
  onFullscreenToggle,
  onBaseLayerChange,
  activeBaseLayer
}) => {
  const map = useMap();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);
  const [drawingMode, setDrawingMode] = useState<string | null>(null);

  // Track mouse position for coordinates and handle map clicks
  useEffect(() => {
    if (!map) return;

    const updateCoordinates = (e: L.LeafletMouseEvent) => {
      setCoordinates({
        lat: e.latlng.lat,
        lng: e.latlng.lng
      });
    };

    const handleMapRightClick = (e: L.LeafletMouseEvent) => {
      // Prevent default context menu
      L.DomEvent.preventDefault(e);

      // Dispatch a custom event with the coordinates
      const customEvent = new CustomEvent('map:clicked', {
        detail: {
          latlng: e.latlng
        }
      });
      window.dispatchEvent(customEvent);
    };

    const handleMapDoubleClick = (e: L.LeafletMouseEvent) => {
      // Dispatch a custom event with the coordinates
      const customEvent = new CustomEvent('map:clicked', {
        detail: {
          latlng: e.latlng
        }
      });
      window.dispatchEvent(customEvent);
    };

    map.on('mousemove', updateCoordinates);
    map.on('contextmenu', handleMapRightClick); // Use contextmenu for right-click
    map.on('dblclick', handleMapDoubleClick);   // Use dblclick for double-click

    return () => {
      map.off('mousemove', updateCoordinates);
      map.off('contextmenu', handleMapRightClick);
      map.off('dblclick', handleMapDoubleClick);
    };
  }, [map]);

  // Initialize fullscreen state tracking
  useEffect(() => {
    if (!map) return;

    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
      if (onFullscreenToggle) {
        onFullscreenToggle();
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [map, onFullscreenToggle]);

  // Handle draw events from MilitaryTools component
  useEffect(() => {
    if (!map || !drawnItems) return;

    // Just handle the draw:created event for callback purposes
    const handleDrawCreated = (e: any) => {
      try {
        if (onDrawCreated) {
          onDrawCreated(e.layer);
        }
        setDrawingMode(null);
      } catch (error) {
        // Import error service
        import('@/services/logging/errorService').then(({ errorService, ErrorCategory }) => {
          errorService.error('Error handling draw created event', ErrorCategory.MAP, error);
        });
      }
    };

    // Remove any existing Leaflet Draw controls that might be visible
    // This ensures our custom military buttons are the only UI for drawing
    try {
      const visibleDrawControls = document.querySelectorAll('.leaflet-draw:not([style*="display: none"])');
      visibleDrawControls.forEach(control => {
        (control as HTMLElement).style.display = 'none';
      });
    } catch (error) {
      console.warn('Error hiding draw controls:', error);
    }

    map.on('draw:created', handleDrawCreated);

    return () => {
      // Cleanup
      map.off('draw:created', handleDrawCreated);
    };
  }, [map, drawnItems, onDrawCreated]);

  // Function to handle base layer change
  const handleBaseLayerChange = (layerName: string) => {
    if (onBaseLayerChange) {
      onBaseLayerChange(layerName);
    }
  };

  // Function to center map on Pakistan
  const centerMap = () => {
    if (map) {
      try {
        console.log("Home button clicked - centering map on Pakistan");

        // Dispatch a custom event to notify MapBounds to skip auto-fitting
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new Event('home-button-clicked'));
        }

        // Use the global method if available
        if (typeof window !== 'undefined' && (window as any).centerMapOnPakistan) {
          (window as any).centerMapOnPakistan();
          return;
        }

        // Fallback to direct approach if global method not available
        // First, cancel any ongoing animations
        map.stop();

        // Force a zoom reset first
        map.setZoom(5, { animate: false });

        // Then set the center directly
        map.panTo([30.3753, 69.3451], {
          animate: false,
          duration: 0
        });

        // Force a redraw immediately
        map.invalidateSize(true);

        console.log("Map centered on Pakistan using fallback method");
      } catch (error) {
        console.warn('Error centering map:', error);

        // Last resort fallback
        try {
          // Simple direct approach as last resort
          map.setView([30.3753, 69.3451], 5, { animate: false });
          console.log("Used last resort method to center map");
        } catch (innerError) {
          console.error('All centering methods failed:', innerError);
        }
      }
    } else {
      console.warn('Map reference not available');
    }
  };

  // Function to clear all drawn items
  const clearDrawnItems = () => {
    if (drawnItems) {
      drawnItems.clearLayers();
    }
  };

  return (
    <>
      {/* Coordinates display */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 z-[1000] military-coordinates">
        {coordinates ? (
          <>
            <span className="mr-2">
              <Crosshair size={10} className="inline mr-1" />
              LAT: {coordinates.lat.toFixed(5)} LON: {coordinates.lng.toFixed(5)}
            </span>
            <span>
              <Grid size={10} className="inline mr-1" />
              ZOOM: {map ? map.getZoom() : ''}
            </span>
          </>
        ) : (
          'AWAITING COORDINATES'
        )}
      </div>

      {/* Base Layers Control - Simplified and moved to top with new icons */}
      <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-[1000] military-control-container rounded-md shadow-lg overflow-hidden">
        <div className="flex space-x-1 p-1">
          <Button
            size="sm"
            variant="ghost"
            className={`military-button ${activeBaseLayer === 'satellite' ? 'active' : ''}`}
            title="Satellite"
            onClick={() => handleBaseLayerChange('satellite')}
          >
            <MapPinned size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className={`military-button ${activeBaseLayer === 'terrain' ? 'active' : ''}`}
            title="Terrain"
            onClick={() => handleBaseLayerChange('terrain')}
          >
            <Mountain size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className={`military-button ${activeBaseLayer === 'osm' ? 'active' : ''}`}
            title="OpenStreetMap"
            onClick={() => handleBaseLayerChange('osm')}
          >
            <Globe size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className={`military-button ${activeBaseLayer === 'dark' ? 'active' : ''}`}
            title="Dark"
            onClick={() => handleBaseLayerChange('dark')}
          >
            <Moon size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className={`military-button ${activeBaseLayer === 'topo' ? 'active' : ''}`}
            title="Topographic"
            onClick={() => handleBaseLayerChange('topo')}
          >
            <Map size={14} />
          </Button>
        </div>
      </div>

      {/* Map control toolbar at bottom center without home button */}
      <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 z-[1000] military-control-container rounded-md shadow-lg overflow-hidden">
        <div className="flex space-x-1 p-1">
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Clear All Drawings"
            onClick={clearDrawnItems}
          >
            <Trash2 size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Add New Incident at Current Location"
            onClick={() => {
              if (coordinates) {
                // Dispatch a custom event with the current coordinates
                // This is still allowed via the button for convenience
                const customEvent = new CustomEvent('map:clicked', {
                  detail: {
                    latlng: { lat: coordinates.lat, lng: coordinates.lng }
                  }
                });
                window.dispatchEvent(customEvent);
              }
            }}
          >
            <MapPin size={14} />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="military-button"
            title="Toggle Fullscreen"
            onClick={() => {
              try {
                if (map && document.fullscreenEnabled) {
                  if (!document.fullscreenElement) {
                    map.getContainer().requestFullscreen()
                      .then(() => {
                        setIsFullscreen(true);
                      })
                      .catch(err => {
                        console.error('Error attempting to enable fullscreen:', err);
                      });
                  } else {
                    document.exitFullscreen()
                      .then(() => {
                        setIsFullscreen(false);
                      })
                      .catch(err => {
                        console.error('Error attempting to exit fullscreen:', err);
                      });
                  }
                }
              } catch (error) {
                console.log('Fullscreen not supported');
                alert('Fullscreen is not supported in your browser');
              }
            }}
          >
            {isFullscreen ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
          </Button>
        </div>
      </div>

      {/* Home button on the right side */}
      <div className="absolute top-2 right-2 z-[1000] military-control-container rounded-md shadow-lg overflow-hidden">
        <div className="flex space-x-1 p-1">
          <div
            className="military-button active cursor-pointer flex items-center justify-center"
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#2a3441',
              color: 'white',
              borderRadius: '4px',
              border: '1px solid #586173',
              clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))'
            }}
            title="Center Map on Pakistan"
            onClick={(e: React.MouseEvent) => {
              e.preventDefault();
              e.stopPropagation();
              // Add a small delay to ensure the event is fully processed
              setTimeout(() => {
                centerMap();
              }, 50);
            }}
            onMouseOver={(e) => {
              (e.currentTarget as HTMLDivElement).style.backgroundColor = '#394049';
            }}
            onMouseOut={(e) => {
              (e.currentTarget as HTMLDivElement).style.backgroundColor = '#2a3441';
            }}
          >
            <Home size={18} /> {/* Increased size for better visibility */}
          </div>
        </div>
      </div>

      {/* Navigation toolbar on the right side */}
      <div className="absolute top-16 right-2 z-[1000] military-control-container rounded-md shadow-lg overflow-hidden">
        <div className="flex flex-col space-y-1 p-1">
          <div
            className="military-button cursor-pointer flex items-center justify-center"
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#2a3441',
              color: 'white',
              borderRadius: '4px',
              border: '1px solid #586173',
              clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))'
            }}
            title="Zoom In"
            onClick={(e: React.MouseEvent) => {
              e.preventDefault();
              e.stopPropagation();

              // Dispatch a custom event to notify MapBounds to skip auto-fitting
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new Event('home-button-clicked'));
              }

              if (map) {
                try {
                  // Stop any ongoing animations
                  map.stop();

                  // Get current zoom level
                  const currentZoom = map.getZoom();

                  // Set new zoom level directly
                  map.setZoom(currentZoom + 1, { animate: false });

                  // Force redraw
                  map.invalidateSize(true);

                  console.log("Zoom in applied, current zoom:", map.getZoom());
                } catch (error) {
                  console.warn('Error zooming in:', error);
                }
              }
            }}
            onMouseOver={(e) => {
              (e.currentTarget as HTMLDivElement).style.backgroundColor = '#394049';
            }}
            onMouseOut={(e) => {
              (e.currentTarget as HTMLDivElement).style.backgroundColor = '#2a3441';
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </div>
          <div
            className="military-button cursor-pointer flex items-center justify-center"
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#2a3441',
              color: 'white',
              borderRadius: '4px',
              border: '1px solid #586173',
              clipPath: 'polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px))'
            }}
            title="Zoom Out"
            onClick={(e: React.MouseEvent) => {
              e.preventDefault();
              e.stopPropagation();

              // Dispatch a custom event to notify MapBounds to skip auto-fitting
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new Event('home-button-clicked'));
              }

              if (map) {
                try {
                  // Stop any ongoing animations
                  map.stop();

                  // Get current zoom level
                  const currentZoom = map.getZoom();

                  // Set new zoom level directly
                  map.setZoom(currentZoom - 1, { animate: false });

                  // Force redraw
                  map.invalidateSize(true);

                  console.log("Zoom out applied, current zoom:", map.getZoom());
                } catch (error) {
                  console.warn('Error zooming out:', error);
                }
              }
            }}
            onMouseOver={(e) => {
              (e.currentTarget as HTMLDivElement).style.backgroundColor = '#394049';
            }}
            onMouseOut={(e) => {
              (e.currentTarget as HTMLDivElement).style.backgroundColor = '#2a3441';
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </div>
        </div>
      </div>
    </>
  );
};

export default MilitaryToolbar;
