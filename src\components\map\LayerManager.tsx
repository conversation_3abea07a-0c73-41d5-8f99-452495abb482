import React, { useState, useRef, useEffect } from 'react';
import L from 'leaflet';
import { useMap } from 'react-leaflet';
import Button from '@/components/ui/Button';
import { Layers, Upload, X, Eye, EyeOff, Trash2 } from 'lucide-react';
import * as shp from 'shpjs';

interface CustomLayer {
  id: string;
  name: string;
  layer: L.Layer;
  visible: boolean;
  type: 'geojson' | 'kml' | 'shapefile';
}

interface LayerManagerProps {
  drawnItems?: L.FeatureGroup | null;
}

const LayerManager: React.FC<LayerManagerProps> = ({ drawnItems }) => {
  const map = useMap();
  const [customLayers, setCustomLayers] = useState<CustomLayer[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);



  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const fileName = file.name;
    const fileExtension = fileName.split('.').pop()?.toLowerCase();

    try {
      if (fileExtension === 'geojson' || fileExtension === 'json') {
        // Handle GeoJSON
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const geojson = JSON.parse(e.target?.result as string);
            addGeoJSONLayer(geojson, fileName);
          } catch (error) {
            console.error('Error parsing GeoJSON:', error);
            alert('Invalid GeoJSON file');
          }
        };
        reader.readAsText(file);
      } else if (fileExtension === 'kml') {
        // Handle KML
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            // Convert KML to GeoJSON using a library (not implemented here)
            // For now, just show an alert
            alert('KML support coming soon!');
          } catch (error) {
            console.error('Error parsing KML:', error);
            alert('Invalid KML file');
          }
        };
        reader.readAsText(file);
      } else if (fileExtension === 'zip') {
        // Handle Shapefile (zipped)
        const reader = new FileReader();
        reader.onload = async (e) => {
          try {
            // @ts-ignore
            const geojson = await shp(e.target.result);
            addGeoJSONLayer(geojson, fileName);
          } catch (error) {
            console.error('Error parsing Shapefile:', error);
            alert('Invalid Shapefile');
          }
        };
        reader.readAsArrayBuffer(file);
      } else {
        alert('Unsupported file format. Please upload GeoJSON, KML, or Shapefile (zipped).');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Error uploading file');
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Add GeoJSON layer to map
  const addGeoJSONLayer = (geojson: any, name: string) => {
    try {
      const layer = L.geoJSON(geojson, {
        style: {
          color: '#3388ff',
          weight: 3,
          opacity: 0.7,
          fillOpacity: 0.2
        },
        pointToLayer: (feature, latlng) => {
          return L.circleMarker(latlng, {
            radius: 8,
            fillColor: '#3388ff',
            color: '#fff',
            weight: 1,
            opacity: 1,
            fillOpacity: 0.8
          });
        }
      });

      // Add layer to map
      layer.addTo(map);

      // Add to custom layers
      const newLayer: CustomLayer = {
        id: `layer-${Date.now()}`,
        name: name,
        layer: layer,
        visible: true,
        type: 'geojson'
      };

      setCustomLayers(prev => [...prev, newLayer]);
    } catch (error) {
      console.error('Error adding GeoJSON layer:', error);
      alert('Error adding layer to map');
    }
  };

  // Toggle layer visibility
  const toggleLayerVisibility = (id: string) => {
    setCustomLayers(prev => prev.map(layer => {
      if (layer.id === id) {
        if (layer.visible) {
          map.removeLayer(layer.layer);
        } else {
          map.addLayer(layer.layer);
        }
        return { ...layer, visible: !layer.visible };
      }
      return layer;
    }));
  };

  // Remove layer
  const removeLayer = (id: string) => {
    const layer = customLayers.find(l => l.id === id);
    if (layer) {
      map.removeLayer(layer.layer);
    }

    setCustomLayers(prev => prev.filter(l => l.id !== id));
  };

  // Function to export drawn items as GeoJSON
  const exportDrawnItems = () => {
    if (!drawnItems || drawnItems.getLayers().length === 0) {
      alert('No drawings to export');
      return;
    }

    try {
      // Convert drawn items to GeoJSON
      const geoJson = drawnItems.toGeoJSON();
      const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(geoJson));
      const downloadAnchorNode = document.createElement('a');
      downloadAnchorNode.setAttribute("href", dataStr);
      downloadAnchorNode.setAttribute("download", "drawings.geojson");
      document.body.appendChild(downloadAnchorNode);
      downloadAnchorNode.click();
      downloadAnchorNode.remove();
    } catch (error) {
      console.error('Error exporting drawings:', error);
      alert('Error exporting drawings');
    }
  };

  return (
    <div className="space-y-3">
      {/* Layer upload section */}
      <div>
        <div className="text-xs font-bold mb-1 text-gray-300">UPLOAD LAYER</div>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileUpload}
          accept=".geojson,.json,.kml,.zip"
          className="hidden"
          id="layer-file-input"
        />
        <Button
          size="sm"
          variant="ghost"
          className="military-button w-full"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload size={14} className="mr-1" />
          Upload Layer
        </Button>
        <div className="text-xs text-gray-500 mt-1">
          GeoJSON, KML, Shapefile (zipped)
        </div>
      </div>

      {/* Export drawings section */}
      {drawnItems && (
        <div>
          <div className="text-xs font-bold mb-1 text-gray-300">EXPORT DRAWINGS</div>
          <Button
            size="sm"
            variant="ghost"
            className="military-button w-full"
            onClick={exportDrawnItems}
          >
            <Upload size={14} className="mr-1" />
            Export Drawings
          </Button>
        </div>
      )}

      {/* Layer management section */}
      <div>
        <div className="text-xs font-bold mb-1 text-gray-300">MANAGE LAYERS</div>
        {customLayers.length > 0 ? (
          <ul className="space-y-2 max-h-[200px] overflow-y-auto">
            {customLayers.map(layer => (
              <li key={layer.id} className="flex items-center justify-between text-xs p-2 bg-gray-800 rounded">
                <div className="flex-1 truncate mr-2">{layer.name}</div>
                <div className="flex space-x-1">
                  <Button
                    size="xs"
                    variant="ghost"
                    className="military-button"
                    onClick={() => toggleLayerVisibility(layer.id)}
                    title={layer.visible ? 'Hide layer' : 'Show layer'}
                  >
                    {layer.visible ? <Eye size={14} /> : <EyeOff size={14} />}
                  </Button>
                  <Button
                    size="xs"
                    variant="ghost"
                    className="military-button"
                    onClick={() => removeLayer(layer.id)}
                    title="Remove layer"
                  >
                    <Trash2 size={14} />
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-xs text-gray-500 text-center py-2">
            No custom layers added
          </div>
        )}
      </div>
    </div>
  );
};

export default LayerManager;
